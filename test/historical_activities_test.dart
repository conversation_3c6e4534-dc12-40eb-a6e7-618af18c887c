import 'package:flutter_test/flutter_test.dart';
import 'package:myrunway/core/models/historical_activity_models.dart';
import 'package:myrunway/widgets/historical_activities_widget.dart';
import 'package:flutter/material.dart';

void main() {
  group('Historical Activities Tests', () {
    test('HistoricalActivityModel fromJson should parse correctly', () {
      final json = {
        'id': '123',
        'timestamp': '2024-01-15T10:30:00Z',
        'activity_type': 'order_created',
        'user_name': 'أحمد محمد',
        'user_id': 1,
        'description': 'تم إنشاء طلب جديد',
        'order_id': 456,
        'order_code': 'ORD-001',
        'customer_name': 'شركة الاختبار',
        'details': {'key': 'value'}
      };

      final activity = HistoricalActivityModel.fromJson(json);

      expect(activity.id, '123');
      expect(activity.activityType, 'order_created');
      expect(activity.userName, 'أحمد محمد');
      expect(activity.userId, 1);
      expect(activity.description, 'تم إنشاء طلب جديد');
      expect(activity.orderId, 456);
      expect(activity.orderCode, 'ORD-001');
      expect(activity.customerName, 'شركة الاختبار');
      expect(activity.details, {'key': 'value'});
    });

    test('HistoricalActivityResponseModel fromJson should parse correctly', () {
      final json = {
        'activities': [
          {
            'id': '123',
            'timestamp': '2024-01-15T10:30:00Z',
            'activity_type': 'order_created',
            'user_name': 'أحمد محمد',
            'user_id': 1,
            'description': 'تم إنشاء طلب جديد',
          }
        ],
        'total_count': 1,
        'page': 1,
        'page_size': 20,
        'has_more': false
      };

      final response = HistoricalActivityResponseModel.fromJson(json);

      expect(response.activities.length, 1);
      expect(response.totalCount, 1);
      expect(response.page, 1);
      expect(response.pageSize, 20);
      expect(response.hasMore, false);
      expect(response.activities.first.id, '123');
    });

    testWidgets('HistoricalActivitiesWidget should display activities', (WidgetTester tester) async {
      final activities = [
        HistoricalActivityModel(
          id: '1',
          timestamp: DateTime.now(),
          activityType: 'order_created',
          userName: 'أحمد محمد',
          userId: 1,
          description: 'تم إنشاء طلب جديد',
        ),
      ];

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: HistoricalActivitiesWidget(
              activities: activities,
              isLoading: false,
            ),
          ),
        ),
      );

      expect(find.text('الأنشطة الأخيرة'), findsOneWidget);
      expect(find.text('تم إنشاء طلب جديد'), findsOneWidget);
      expect(find.text('بواسطة أحمد محمد'), findsOneWidget);
    });

    testWidgets('HistoricalActivitiesWidget should show loading indicator', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: HistoricalActivitiesWidget(
              activities: [],
              isLoading: true,
            ),
          ),
        ),
      );

      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('HistoricalActivitiesWidget should show empty state', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: HistoricalActivitiesWidget(
              activities: [],
              isLoading: false,
            ),
          ),
        ),
      );

      expect(find.text('لا توجد أنشطة حديثة'), findsOneWidget);
    });
  });
}
