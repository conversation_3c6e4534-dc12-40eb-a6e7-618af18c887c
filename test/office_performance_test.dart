import 'package:flutter_test/flutter_test.dart';
import 'package:myrunway/core/models/office_performance_models.dart';

void main() {
  group('Office Performance Tests', () {
    test('OfficePerformanceModel fromJson should parse correctly', () {
      final json = {
        'total_orders': 150,
        'orders_by_status': {'pending': 20, 'completed': 100, 'cancelled': 30},
        'orders_by_delivery_status': {'delivered': 90, 'pending': 60},
        'total_revenue': 75000.50,
        'average_order_value': 500.0,
        'collection_rate': 0.85,
        'average_completion_time': 2.5,
        'on_time_delivery_rate': 0.92,
        'orders_by_company': [
          {
            'company_name': 'شركة الاختبار',
            'order_count': 50,
            'total_revenue': 25000.0,
          },
        ],
        'top_performing_employees': [
          {
            'employee_name': 'أحمد محمد',
            'orders_completed': 25,
            'total_revenue': 12500.0,
            'completion_rate': 0.95,
          },
        ],
        'daily_performance': [
          {
            'date': '2024-01-15',
            'orders_count': 10,
            'revenue': 5000.0,
            'completion_rate': 0.9,
          },
        ],
      };

      final performance = OfficePerformanceModel.fromJson(json);

      expect(performance.totalOrders, 150);
      expect(performance.ordersByStatus['pending'], 20);
      expect(performance.ordersByStatus['completed'], 100);
      expect(performance.totalRevenue, 75000.50);
      expect(performance.averageOrderValue, 500.0);
      expect(performance.collectionRate, 0.85);
      expect(performance.averageCompletionTime, 2.5);
      expect(performance.onTimeDeliveryRate, 0.92);
      expect(performance.ordersByCompany.length, 1);
      expect(performance.topPerformingEmployees.length, 1);
      expect(performance.dailyPerformance.length, 1);
    });

    test('CompanyPerformanceModel fromJson should parse correctly', () {
      final json = {
        'company_name': 'شركة الاختبار',
        'order_count': 50,
        'total_revenue': 25000.0,
      };

      final company = CompanyPerformanceModel.fromJson(json);

      expect(company.companyName, 'شركة الاختبار');
      expect(company.orderCount, 50);
      expect(company.totalRevenue, 25000.0);
    });

    test('EmployeePerformanceModel fromJson should parse correctly', () {
      final json = {
        'employee_name': 'أحمد محمد',
        'orders_completed': 25,
        'total_revenue': 12500.0,
        'completion_rate': 0.95,
      };

      final employee = EmployeePerformanceModel.fromJson(json);

      expect(employee.employeeName, 'أحمد محمد');
      expect(employee.ordersCompleted, 25);
      expect(employee.totalRevenue, 12500.0);
      expect(employee.completionRate, 0.95);
    });

    test('DailyPerformanceModel fromJson should parse correctly', () {
      final json = {
        'date': '2024-01-15',
        'orders_count': 10,
        'revenue': 5000.0,
        'completion_rate': 0.9,
      };

      final daily = DailyPerformanceModel.fromJson(json);

      expect(daily.date, DateTime.parse('2024-01-15'));
      expect(daily.ordersCount, 10);
      expect(daily.revenue, 5000.0);
      expect(daily.completionRate, 0.9);
    });

    test('DateRangeModel toJson should format correctly', () {
      final dateRange = DateRangeModel(
        startDate: DateTime(2024, 1, 1),
        endDate: DateTime(2024, 1, 31),
      );

      final json = dateRange.toJson();

      expect(json['start_date'], '2024-01-01');
      expect(json['end_date'], '2024-01-31');
    });

    // Widget tests removed for simplicity - focusing on model tests only
  });
}
