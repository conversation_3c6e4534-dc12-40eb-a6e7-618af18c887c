import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:get/get.dart';
import 'package:logging/logging.dart';
import 'package:myrunway/core/constants/user_roles.dart';
import 'package:myrunway/core/services/auth_service.dart';
import 'package:myrunway/pages/auth/login_page.dart';
import 'package:myrunway/pages/employee/employee_entry_page.dart';
import 'package:myrunway/pages/manager/home_page.dart';
import 'package:myrunway/pages/master/home_page.dart';

final _logger = Logger('MyApp');

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    final userRole = AuthService.to.currentUserRole;
    Widget homePage;
    switch (userRole) {
      case UserRole.employee:
        _logger.info('Navigating to Employee home page');
        homePage = const EmployeeEntryPage();
        break;
      case UserRole.manager:
        _logger.info('Navigating to <PERSON> home page');
        homePage = const ManagerHomePage();
        break;
      case UserRole.master:
        _logger.info('Navigating to Master home page');
        homePage = const MasterHomePage();
        break;
      default:
        _logger.warning('No home page found for user role: $userRole');
        homePage = const LoginPage();
        break;
    }
    return GetMaterialApp(
      title: 'MyRunway - إدارة الطلبات',
      locale: const Locale('ar', 'SA'),
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: const [Locale('ar', 'SA')],
      theme: ThemeData(
        primarySwatch: Colors.deepPurple,
        fontFamily: 'Arial',
        visualDensity: VisualDensity.adaptivePlatformDensity,
        appBarTheme: const AppBarTheme(
          backgroundColor: Color(0xFFF8F9FA),
          foregroundColor: Colors.black87,
          elevation: 0,
        ),
        cardTheme: CardTheme(
          elevation: 0,
          color: Colors.white,
          shadowColor: Colors.purpleAccent.withValues(alpha: 0.2),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(21),
          ),
        ),
      ),
      home: homePage,
      debugShowCheckedModeBanner: false,
    );
  }
}
