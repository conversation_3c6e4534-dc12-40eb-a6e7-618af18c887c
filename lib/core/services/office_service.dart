import 'package:get/get.dart';
import 'package:myrunway/core/models/historical_activity_models.dart';
import 'package:myrunway/core/models/office_performance_models.dart';
import 'package:myrunway/core/services/api_service.dart';
import 'package:myrunway/core/constants/api_endpoints.dart';

class OfficeService extends GetxService {
  final ApiService _apiService = Get.find<ApiService>();

  // Get historical activities for the office
  Future<ApiResponse<HistoricalActivityResponseModel>> getHistoricalActivities({
    int page = 1,
    int pageSize = 20,
  }) async {
    final response = await _apiService.get<Map<String, dynamic>>(
      ApiEndpoints.historicalActivities,
      (data) => data as Map<String, dynamic>,
      query: {'page': page, 'page_size': pageSize},
    );

    if (response.success && response.data != null) {
      final historicalActivities = HistoricalActivityResponseModel.fromJson(
        response.data!,
      );
      return ApiResponse.success(historicalActivities);
    }

    return ApiResponse.error(
      response.message ?? 'فشل في جلب الأنشطة التاريخية',
      statusCode: response.statusCode,
    );
  }

  // Get office performance metrics
  Future<ApiResponse<OfficePerformanceModel>> getOfficePerformance({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    // Default to last 30 days if no date range provided
    final defaultEndDate = endDate ?? DateTime.now();
    final defaultStartDate =
        startDate ?? defaultEndDate.subtract(const Duration(days: 30));

    final dateRange = DateRangeModel(
      startDate: defaultStartDate,
      endDate: defaultEndDate,
    );

    final response = await _apiService.post<Map<String, dynamic>>(
      ApiEndpoints.officePerformance,
      (data) => data as Map<String, dynamic>,
      body: dateRange.toJson(),
    );

    if (response.success && response.data != null) {
      final officePerformance = OfficePerformanceModel.fromJson(response.data!);
      return ApiResponse.success(officePerformance);
    }

    return ApiResponse.error(
      response.message ?? 'فشل في جلب بيانات أداء المكتب',
      statusCode: response.statusCode,
    );
  }
}
