import 'package:get/get.dart';
import 'package:myrunway/core/services/api_service.dart';
import 'package:myrunway/core/services/order_service.dart';
import 'package:myrunway/core/services/auth_service.dart';
import 'package:myrunway/core/models/order_model_new.dart';
import 'package:myrunway/core/constants/user_roles.dart';

// Autocomplete suggestion model
class AutocompleteSuggestion {
  final String value;
  final String? displayText;
  final Map<String, dynamic>? metadata;

  AutocompleteSuggestion({
    required this.value,
    this.displayText,
    this.metadata,
  });

  String get display => displayText ?? value;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AutocompleteSuggestion &&
          runtimeType == other.runtimeType &&
          value == other.value;

  @override
  int get hashCode => value.hashCode;
}

// Autocomplete service for order-related data
class AutocompleteService extends GetxService {
  final OrderService _orderService = Get.find<OrderService>();
  final AuthService _authService = Get.find<AuthService>();

  // Cache for autocomplete data to improve performance
  final Map<String, List<AutocompleteSuggestion>> _cache = {};
  final Map<String, DateTime> _cacheTimestamps = {};
  static const Duration _cacheExpiry = Duration(minutes: 5);

  // Get customer name suggestions
  Future<List<AutocompleteSuggestion>> getCustomerNameSuggestions(
    String query, {
    int limit = 10,
  }) async {
    if (query.trim().isEmpty) return [];

    // Check permissions - only allow for users with order access
    if (!_hasOrderAccess()) {
      return [];
    }

    final cacheKey = 'customer_names_$query_${_authService.currentUser?.id}';
    if (_isCacheValid(cacheKey)) {
      return _cache[cacheKey]!
          .where(
            (suggestion) =>
                suggestion.value.toLowerCase().contains(query.toLowerCase()),
          )
          .take(limit)
          .toList();
    }

    try {
      // Fetch recent orders to extract customer names
      final response = await _orderService.getOrders();
      if (response.success && response.data != null) {
        final customerNames = <String, AutocompleteSuggestion>{};

        // Filter orders based on user role and office access
        final filteredOrders = _filterOrdersByRole(response.data!);

        for (final order in filteredOrders) {
          final name = order.customerName.trim();
          if (name.isNotEmpty &&
              name.toLowerCase().contains(query.toLowerCase())) {
            customerNames[name] = AutocompleteSuggestion(
              value: name,
              metadata: {
                'phone': order.customerPhone,
                'address': order.customerAddress,
                'company': order.customerCompany?.name,
                'officeId': order.office.id,
              },
            );
          }
        }

        final suggestions = customerNames.values.toList();
        _updateCache(cacheKey, suggestions);

        return suggestions.take(limit).toList();
      }
    } catch (e) {
      // Return empty list on error
    }

    return [];
  }

  // Get customer address suggestions with fuzzy matching for Arabic
  Future<List<AutocompleteSuggestion>> getCustomerAddressSuggestions(
    String query, {
    int limit = 10,
    double similarityThreshold = 0.75,
  }) async {
    if (query.trim().isEmpty) return [];

    final cacheKey = 'customer_addresses_$query';
    if (_isCacheValid(cacheKey)) {
      return _cache[cacheKey]!
          .where(
            (suggestion) =>
                _calculateSimilarity(suggestion.value, query) >=
                similarityThreshold,
          )
          .take(limit)
          .toList();
    }

    try {
      final response = await _orderService.getOrders();
      if (response.success && response.data != null) {
        final addresses = <String, AutocompleteSuggestion>{};

        for (final order in response.data!) {
          final address = order.customerAddress?.trim();
          if (address != null && address.isNotEmpty) {
            final similarity = _calculateSimilarity(address, query);
            if (similarity >= similarityThreshold) {
              addresses[address] = AutocompleteSuggestion(
                value: address,
                metadata: {
                  'customerName': order.customerName,
                  'phone': order.customerPhone,
                  'similarity': similarity,
                },
              );
            }
          }
        }

        final suggestions =
            addresses.values.toList()..sort(
              (a, b) => (b.metadata?['similarity'] ?? 0.0).compareTo(
                a.metadata?['similarity'] ?? 0.0,
              ),
            );

        _updateCache(cacheKey, suggestions);
        return suggestions.take(limit).toList();
      }
    } catch (e) {
      // Return empty list on error
    }

    return [];
  }

  // Get customer phone suggestions
  Future<List<AutocompleteSuggestion>> getCustomerPhoneSuggestions(
    String query, {
    int limit = 10,
  }) async {
    if (query.trim().isEmpty) return [];

    final cacheKey = 'customer_phones_$query';
    if (_isCacheValid(cacheKey)) {
      return _cache[cacheKey]!
          .where((suggestion) => suggestion.value.contains(query))
          .take(limit)
          .toList();
    }

    try {
      final response = await _orderService.getOrders();
      if (response.success && response.data != null) {
        final phones = <String, AutocompleteSuggestion>{};

        for (final order in response.data!) {
          final phone = order.customerPhone.trim();
          if (phone.isNotEmpty && phone.contains(query)) {
            phones[phone] = AutocompleteSuggestion(
              value: phone,
              displayText: '$phone (${order.customerName})',
              metadata: {
                'customerName': order.customerName,
                'address': order.customerAddress,
              },
            );
          }
        }

        final suggestions = phones.values.toList();
        _updateCache(cacheKey, suggestions);

        return suggestions.take(limit).toList();
      }
    } catch (e) {
      // Return empty list on error
    }

    return [];
  }

  // Get order code suggestions to prevent duplicates
  Future<List<AutocompleteSuggestion>> getOrderCodeSuggestions(
    String query, {
    int limit = 10,
  }) async {
    if (query.trim().isEmpty) return [];

    try {
      final response = await _orderService.getOrders();
      if (response.success && response.data != null) {
        final existingCodes =
            response.data!
                .map((order) => order.code)
                .where(
                  (code) => code.toLowerCase().contains(query.toLowerCase()),
                )
                .toSet()
                .map(
                  (code) => AutocompleteSuggestion(
                    value: code,
                    displayText: '$code (موجود)',
                    metadata: {'exists': true},
                  ),
                )
                .toList();

        // Generate suggested patterns based on existing codes
        final suggestions = <AutocompleteSuggestion>[];
        suggestions.addAll(existingCodes);

        // Add pattern suggestions if query looks like a code pattern
        if (query.length >= 2) {
          final pattern = query.toUpperCase();
          for (int i = 1; i <= 999; i++) {
            final suggestedCode = '$pattern${i.toString().padLeft(3, '0')}';
            if (!existingCodes.any((s) => s.value == suggestedCode)) {
              suggestions.add(
                AutocompleteSuggestion(
                  value: suggestedCode,
                  displayText: '$suggestedCode (مقترح)',
                  metadata: {'suggested': true},
                ),
              );
              if (suggestions.length >= limit) break;
            }
          }
        }

        return suggestions.take(limit).toList();
      }
    } catch (e) {
      // Return empty list on error
    }

    return [];
  }

  // Clear cache
  void clearCache() {
    _cache.clear();
    _cacheTimestamps.clear();
  }

  // Clear specific cache entry
  void clearCacheEntry(String key) {
    _cache.remove(key);
    _cacheTimestamps.remove(key);
  }

  // Check if cache is valid
  bool _isCacheValid(String key) {
    if (!_cache.containsKey(key) || !_cacheTimestamps.containsKey(key)) {
      return false;
    }

    final timestamp = _cacheTimestamps[key]!;
    return DateTime.now().difference(timestamp) < _cacheExpiry;
  }

  // Update cache
  void _updateCache(String key, List<AutocompleteSuggestion> suggestions) {
    _cache[key] = suggestions;
    _cacheTimestamps[key] = DateTime.now();
  }

  // Calculate similarity between two strings (fuzzy matching for Arabic)
  double _calculateSimilarity(String str1, String str2) {
    if (str1.isEmpty && str2.isEmpty) return 1.0;
    if (str1.isEmpty || str2.isEmpty) return 0.0;

    // Normalize Arabic text for better matching
    final normalized1 = _normalizeArabicText(str1.toLowerCase());
    final normalized2 = _normalizeArabicText(str2.toLowerCase());

    // Use Levenshtein distance for similarity calculation
    final distance = _levenshteinDistance(normalized1, normalized2);
    final maxLength = [
      normalized1.length,
      normalized2.length,
    ].reduce((a, b) => a > b ? a : b);

    return 1.0 - (distance / maxLength);
  }

  // Normalize Arabic text for better matching
  String _normalizeArabicText(String text) {
    return text
        // Remove diacritics
        .replaceAll(RegExp(r'[\u064B-\u065F\u0670\u0640]'), '')
        // Normalize common abbreviations
        .replaceAll('شارع', 'ش')
        .replaceAll('طريق', 'ط')
        .replaceAll('منطقة', 'م')
        .replaceAll('حي', 'ح')
        // Remove extra spaces
        .replaceAll(RegExp(r'\s+'), ' ')
        .trim();
  }

  // Calculate Levenshtein distance between two strings
  int _levenshteinDistance(String str1, String str2) {
    final len1 = str1.length;
    final len2 = str2.length;

    // Create a matrix to store distances
    final matrix = List.generate(
      len1 + 1,
      (i) => List.generate(len2 + 1, (j) => 0),
    );

    // Initialize first row and column
    for (int i = 0; i <= len1; i++) {
      matrix[i][0] = i;
    }
    for (int j = 0; j <= len2; j++) {
      matrix[0][j] = j;
    }

    // Fill the matrix
    for (int i = 1; i <= len1; i++) {
      for (int j = 1; j <= len2; j++) {
        final cost = str1[i - 1] == str2[j - 1] ? 0 : 1;
        matrix[i][j] = [
          matrix[i - 1][j] + 1, // deletion
          matrix[i][j - 1] + 1, // insertion
          matrix[i - 1][j - 1] + cost, // substitution
        ].reduce((a, b) => a < b ? a : b);
      }
    }

    return matrix[len1][len2];
  }

  // Get combined customer data suggestions (name + phone + address)
  Future<List<AutocompleteSuggestion>> getCustomerDataSuggestions(
    String query, {
    int limit = 10,
  }) async {
    if (query.trim().isEmpty) return [];

    try {
      final response = await _orderService.getOrders();
      if (response.success && response.data != null) {
        final customers = <String, AutocompleteSuggestion>{};

        for (final order in response.data!) {
          final name = order.customerName.trim();
          final phone = order.customerPhone.trim();
          final address = order.customerAddress?.trim() ?? '';

          // Check if query matches any customer field
          final nameMatch = name.toLowerCase().contains(query.toLowerCase());
          final phoneMatch = phone.contains(query);
          final addressMatch =
              address.isNotEmpty && _calculateSimilarity(address, query) >= 0.5;

          if (nameMatch || phoneMatch || addressMatch) {
            final key = '$name-$phone';
            customers[key] = AutocompleteSuggestion(
              value: name,
              displayText: '$name - $phone',
              metadata: {
                'name': name,
                'phone': phone,
                'address': address,
                'company': order.customerCompany?.name,
                'nameMatch': nameMatch,
                'phoneMatch': phoneMatch,
                'addressMatch': addressMatch,
              },
            );
          }
        }

        final suggestions = customers.values.toList();

        // Sort by relevance (name matches first, then phone, then address)
        suggestions.sort((a, b) {
          final aNameMatch = a.metadata?['nameMatch'] ?? false;
          final bNameMatch = b.metadata?['nameMatch'] ?? false;
          final aPhoneMatch = a.metadata?['phoneMatch'] ?? false;
          final bPhoneMatch = b.metadata?['phoneMatch'] ?? false;

          if (aNameMatch && !bNameMatch) return -1;
          if (!aNameMatch && bNameMatch) return 1;
          if (aPhoneMatch && !bPhoneMatch) return -1;
          if (!aPhoneMatch && bPhoneMatch) return 1;

          return a.value.compareTo(b.value);
        });

        return suggestions.take(limit).toList();
      }
    } catch (e) {
      // Return empty list on error
    }

    return [];
  }
}
