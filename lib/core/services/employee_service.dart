import 'package:get/get.dart';
import 'package:myrunway/core/models/employee_performance_models.dart';
import 'package:myrunway/core/models/employee_balance_models.dart';
import 'package:myrunway/core/models/employee_stats_model.dart';
import 'package:myrunway/core/services/api_service.dart';
import 'package:myrunway/core/constants/api_endpoints.dart';

class EmployeeService extends GetxService {
  final ApiService _apiService = Get.find<ApiService>();

  // Get employee performance metrics
  Future<ApiResponse<EmployeePerformanceModel>> getEmployeePerformance(
    int employeeId, {
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    // Default to last 30 days if no date range provided
    final defaultEndDate = endDate ?? DateTime.now();
    final defaultStartDate =
        startDate ?? defaultEndDate.subtract(const Duration(days: 30));

    final dateRange = DateRangeModel(
      startDate: defaultStartDate,
      endDate: defaultEndDate,
    );

    final response = await _apiService.post<Map<String, dynamic>>(
      ApiEndpoints.officeEmployeePerformanceById(employeeId.toString()),
      (data) => data as Map<String, dynamic>,
      body: dateRange.toJson(),
    );

    if (response.success && response.data != null) {
      final employeePerformance = EmployeePerformanceModel.fromJson(
        response.data!,
      );
      return ApiResponse.success(employeePerformance);
    }

    return ApiResponse.error(
      response.message ?? 'فشل في جلب بيانات أداء الموظف',
      statusCode: response.statusCode,
      errors: response.errors,
    );
  }

  // Get employee performance with predefined date ranges
  Future<ApiResponse<EmployeePerformanceModel>> getEmployeePerformanceLastWeek(
    int employeeId,
  ) async {
    final dateRange = DateRangeModel.lastWeek();
    return getEmployeePerformance(
      employeeId,
      startDate: dateRange.startDate,
      endDate: dateRange.endDate,
    );
  }

  Future<ApiResponse<EmployeePerformanceModel>> getEmployeePerformanceLastMonth(
    int employeeId,
  ) async {
    final dateRange = DateRangeModel.lastMonth();
    return getEmployeePerformance(
      employeeId,
      startDate: dateRange.startDate,
      endDate: dateRange.endDate,
    );
  }

  Future<ApiResponse<EmployeePerformanceModel>>
  getEmployeePerformanceLast30Days(int employeeId) async {
    final dateRange = DateRangeModel.last30Days();
    return getEmployeePerformance(
      employeeId,
      startDate: dateRange.startDate,
      endDate: dateRange.endDate,
    );
  }

  // Get employee performance for current month
  Future<ApiResponse<EmployeePerformanceModel>>
  getEmployeePerformanceCurrentMonth(int employeeId) async {
    final now = DateTime.now();
    final startOfMonth = DateTime(now.year, now.month, 1);

    return getEmployeePerformance(
      employeeId,
      startDate: startOfMonth,
      endDate: now,
    );
  }

  // Get employee performance for current week
  Future<ApiResponse<EmployeePerformanceModel>>
  getEmployeePerformanceCurrentWeek(int employeeId) async {
    final now = DateTime.now();
    final startOfWeek = now.subtract(Duration(days: now.weekday - 1));

    return getEmployeePerformance(
      employeeId,
      startDate: startOfWeek,
      endDate: now,
    );
  }

  // Get employee performance for today
  Future<ApiResponse<EmployeePerformanceModel>> getEmployeePerformanceToday(
    int employeeId,
  ) async {
    final now = DateTime.now();
    final startOfDay = DateTime(now.year, now.month, now.day);

    return getEmployeePerformance(
      employeeId,
      startDate: startOfDay,
      endDate: now,
    );
  }

  // Get employee performance for yesterday
  Future<ApiResponse<EmployeePerformanceModel>> getEmployeePerformanceYesterday(
    int employeeId,
  ) async {
    final yesterday = DateTime.now().subtract(const Duration(days: 1));
    final startOfDay = DateTime(yesterday.year, yesterday.month, yesterday.day);
    final endOfDay = DateTime(
      yesterday.year,
      yesterday.month,
      yesterday.day,
      23,
      59,
      59,
    );

    return getEmployeePerformance(
      employeeId,
      startDate: startOfDay,
      endDate: endOfDay,
    );
  }

  // Helper method to get performance for custom date range
  Future<ApiResponse<EmployeePerformanceModel>>
  getEmployeePerformanceCustomRange(
    int employeeId,
    DateRangeModel dateRange,
  ) async {
    return getEmployeePerformance(
      employeeId,
      startDate: dateRange.startDate,
      endDate: dateRange.endDate,
    );
  }

  // Get employee statistics
  Future<ApiResponse<EmployeeStatsModel>> getEmployeeStats(
    int employeeId, {
    DateTime? dateFrom,
    DateTime? dateTo,
  }) async {
    Map<String, dynamic>? queryParams;

    if (dateFrom != null || dateTo != null) {
      queryParams = {};
      if (dateFrom != null) {
        queryParams['date_from'] = dateFrom.toIso8601String();
      }
      if (dateTo != null) {
        queryParams['date_to'] = dateTo.toIso8601String();
      }
    }

    final response = await _apiService.get<Map<String, dynamic>>(
      ApiEndpoints.accountsEmployeeStatsById(employeeId.toString()),
      (data) => data as Map<String, dynamic>,
      query: queryParams,
    );

    if (response.success && response.data != null) {
      final employeeStats = EmployeeStatsModel.fromJson(response.data!);
      return ApiResponse.success(employeeStats);
    }

    return ApiResponse.error(
      response.message ?? 'فشل في جلب إحصائيات الموظف',
      statusCode: response.statusCode,
      errors: response.errors,
    );
  }

  // Get employee balance transactions
  Future<ApiResponse<List<EmployeeBalanceTransactionModel>>>
  getEmployeeBalanceTransactions(int employeeId) async {
    final response = await _apiService.get<List<dynamic>>(
      ApiEndpoints.employeeBalanceTransactionsById(employeeId.toString()),
      (data) => data as List<dynamic>,
    );

    if (response.success && response.data != null) {
      final transactions =
          response.data!
              .map(
                (json) => EmployeeBalanceTransactionModel.fromJson(
                  json as Map<String, dynamic>,
                ),
              )
              .toList();
      return ApiResponse.success(transactions);
    }

    return ApiResponse.error(
      response.message ?? 'فشل في جلب معاملات الرصيد',
      statusCode: response.statusCode,
      errors: response.errors,
    );
  }

  // Settle employee balance
  Future<ApiResponse<EmployeeBalanceTransactionModel>> settleEmployeeBalance(
    int employeeId, {
    String? notes,
  }) async {
    final settlementRequest = EmployeeSettlementRequest(notes: notes);

    final response = await _apiService.post<Map<String, dynamic>>(
      ApiEndpoints.employeeSettleBalanceById(employeeId.toString()),
      (data) => data as Map<String, dynamic>,
      body: settlementRequest.toJson(),
    );

    if (response.success && response.data != null) {
      final transaction = EmployeeBalanceTransactionModel.fromJson(
        response.data!,
      );
      return ApiResponse.success(transaction);
    }

    return ApiResponse.error(
      response.message ?? 'فشل في تسوية رصيد الموظف',
      statusCode: response.statusCode,
      errors: response.errors,
    );
  }

  // Get employee balance summary from transactions
  Future<ApiResponse<EmployeeBalanceSummary>> getEmployeeBalanceSummary(
    int employeeId,
  ) async {
    final transactionsResponse = await getEmployeeBalanceTransactions(
      employeeId,
    );

    if (transactionsResponse.success && transactionsResponse.data != null) {
      final summary = EmployeeBalanceSummary.fromTransactions(
        transactionsResponse.data!,
      );
      return ApiResponse.success(summary);
    }

    return ApiResponse.error(
      transactionsResponse.message ?? 'فشل في جلب ملخص الرصيد',
      statusCode: transactionsResponse.statusCode,
      errors: transactionsResponse.errors,
    );
  }
}
