import 'package:get/get.dart';
import 'package:myrunway/core/services/api_service.dart';
import 'package:myrunway/core/constants/api_endpoints.dart';
import 'package:myrunway/core/models/company_channel_model.dart';

class CompanyChannelService extends GetxService {
  final ApiService _apiService = Get.find<ApiService>();

  // Get all company channels with optional company filter
  Future<ApiResponse<List<CompanyChannelModel>>> getCompanyChannels({
    int? companyId,
  }) async {
    final Map<String, dynamic> query = {};
    if (companyId != null) query['company_id'] = companyId;

    final response = await _apiService.get<List<dynamic>>(
      ApiEndpoints.companyChannels,
      (data) => data as List<dynamic>,
      query: query.isNotEmpty ? query : null,
    );

    if (response.success && response.data != null) {
      final channels = response.data!
          .map((json) => CompanyChannelModel.fromJson(json as Map<String, dynamic>))
          .toList();
      return ApiResponse.success(channels);
    }

    return ApiResponse.error(
      response.message ?? 'فشل في جلب قائمة قنوات الشركات',
      statusCode: response.statusCode,
    );
  }

  // Get company channel by ID
  Future<ApiResponse<CompanyChannelModel>> getCompanyChannelById(int channelId) async {
    final response = await _apiService.get<Map<String, dynamic>>(
      ApiEndpoints.companyChannelById(channelId.toString()),
      (data) => data as Map<String, dynamic>,
    );

    if (response.success && response.data != null) {
      final channel = CompanyChannelModel.fromJson(response.data!);
      return ApiResponse.success(channel);
    }

    return ApiResponse.error(
      response.message ?? 'فشل في جلب بيانات القناة',
      statusCode: response.statusCode,
    );
  }

  // Create a new company channel
  Future<ApiResponse<CompanyChannelModel>> createCompanyChannel(
    CompanyChannelCreateRequest request,
  ) async {
    // Validate request
    if (!request.isValid) {
      return ApiResponse.error(request.validationError ?? 'بيانات غير صحيحة');
    }

    final response = await _apiService.post<Map<String, dynamic>>(
      ApiEndpoints.createCompanyChannel,
      (data) => data as Map<String, dynamic>,
      body: request.toJson(),
    );

    if (response.success && response.data != null) {
      final channel = CompanyChannelModel.fromJson(response.data!);
      return ApiResponse.success(channel);
    }

    return ApiResponse.error(
      response.message ?? 'فشل في إنشاء القناة',
      statusCode: response.statusCode,
      errors: response.errors,
    );
  }

  // Update an existing company channel
  Future<ApiResponse<CompanyChannelModel>> updateCompanyChannel(
    int channelId,
    CompanyChannelEditRequest request,
  ) async {
    // Check if request has data to update
    if (request.isEmpty) {
      return ApiResponse.error('لا توجد بيانات للتحديث');
    }

    // Validate request
    final validationError = request.validationError;
    if (validationError != null) {
      return ApiResponse.error(validationError);
    }

    final response = await _apiService.put<Map<String, dynamic>>(
      ApiEndpoints.companyChannelById(channelId.toString()),
      (data) => data as Map<String, dynamic>,
      body: request.toJson(),
    );

    if (response.success && response.data != null) {
      final channel = CompanyChannelModel.fromJson(response.data!);
      return ApiResponse.success(channel);
    }

    return ApiResponse.error(
      response.message ?? 'فشل في تحديث القناة',
      statusCode: response.statusCode,
      errors: response.errors,
    );
  }

  // Delete a company channel
  Future<ApiResponse<bool>> deleteCompanyChannel(int channelId) async {
    final response = await _apiService.delete<bool>(
      ApiEndpoints.companyChannelById(channelId.toString()),
      (data) => true, // Delete operations typically return success status
    );

    if (response.success) {
      return ApiResponse.success(true, message: 'تم حذف القناة بنجاح');
    }

    return ApiResponse.error(
      response.message ?? 'فشل في حذف القناة',
      statusCode: response.statusCode,
    );
  }

  // Get channels for a specific company (convenience method)
  Future<ApiResponse<List<CompanyChannelModel>>> getChannelsForCompany(int companyId) async {
    return getCompanyChannels(companyId: companyId);
  }

  // Validate WhatsApp number format (basic validation)
  bool isValidWhatsAppNumber(String number) {
    // Remove any non-digit characters
    final cleanNumber = number.replaceAll(RegExp(r'[^\d]'), '');
    
    // Check if it's at least 10 digits and starts with valid country codes
    if (cleanNumber.length < 10) return false;
    
    // Basic validation for common formats
    // This can be enhanced based on specific requirements
    return true;
  }

  // Format WhatsApp number for consistency
  String formatWhatsAppNumber(String number) {
    // Remove any non-digit characters
    final cleanNumber = number.replaceAll(RegExp(r'[^\d]'), '');
    
    // Add country code if missing (assuming Egypt +20)
    if (cleanNumber.length == 10 && !cleanNumber.startsWith('20')) {
      return '20$cleanNumber';
    }
    
    return cleanNumber;
  }
}
