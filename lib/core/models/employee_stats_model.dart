class EmployeeStatsModel {
  final double totalMoneyDeserved;
  final int totalOrdersCompleted;
  final int totalOrdersAssigned;
  final double totalMoneyToCollect;
  final double totalMoneyCollected;
  final DateTime? lastUpdated;

  EmployeeStatsModel({
    required this.totalMoneyDeserved,
    required this.totalOrdersCompleted,
    required this.totalOrdersAssigned,
    required this.totalMoneyToCollect,
    required this.totalMoneyCollected,
    this.lastUpdated,
  });

  factory EmployeeStatsModel.fromJson(Map<String, dynamic> json) {
    return EmployeeStatsModel(
      totalMoneyDeserved: (json['total_money_deserved'] ?? 0.0).toDouble(),
      totalOrdersCompleted: json['total_orders_completed'] ?? 0,
      totalOrdersAssigned: json['total_orders_assigned'] ?? 0,
      totalMoneyToCollect: (json['total_money_to_collect'] ?? 0.0).toDouble(),
      totalMoneyCollected: (json['total_money_collected'] ?? 0.0).toDouble(),
      lastUpdated:
          json['last_updated'] != null
              ? DateTime.parse(json['last_updated'])
              : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'total_money_deserved': totalMoneyDeserved,
      'total_orders_completed': totalOrdersCompleted,
      'total_orders_assigned': totalOrdersAssigned,
      'total_money_to_collect': totalMoneyToCollect,
      'total_money_collected': totalMoneyCollected,
      'last_updated': lastUpdated?.toIso8601String(),
    };
  }

  // Helper getters for formatted display
  String get formattedTotalMoneyDeserved {
    return '${totalMoneyDeserved.toStringAsFixed(2)} جنيه';
  }

  String get formattedTotalMoneyToCollect {
    return '${totalMoneyToCollect.toStringAsFixed(2)} جنيه';
  }

  String get formattedTotalMoneyCollected {
    return '${totalMoneyCollected.toStringAsFixed(2)} جنيه';
  }

  double get completionRate {
    if (totalOrdersAssigned == 0) return 0.0;
    return totalOrdersCompleted / totalOrdersAssigned;
  }

  String get formattedCompletionRate {
    return '${(completionRate * 100).toStringAsFixed(1)}%';
  }

  // Create empty stats for loading state
  factory EmployeeStatsModel.empty() {
    return EmployeeStatsModel(
      totalMoneyDeserved: 0.0,
      totalOrdersCompleted: 0,
      totalOrdersAssigned: 0,
      totalMoneyToCollect: 0.0,
      totalMoneyCollected: 0.0,
    );
  }

  @override
  String toString() {
    return 'EmployeeStatsModel(totalMoneyDeserved: $totalMoneyDeserved, totalOrdersCompleted: $totalOrdersCompleted, totalOrdersAssigned: $totalOrdersAssigned, totalMoneyToCollect: $totalMoneyToCollect, totalMoneyCollected: $totalMoneyCollected)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is EmployeeStatsModel &&
        other.totalMoneyDeserved == totalMoneyDeserved &&
        other.totalOrdersCompleted == totalOrdersCompleted &&
        other.totalOrdersAssigned == totalOrdersAssigned &&
        other.totalMoneyToCollect == totalMoneyToCollect &&
        other.totalMoneyCollected == totalMoneyCollected &&
        other.lastUpdated == lastUpdated;
  }

  @override
  int get hashCode {
    return Object.hash(
      totalMoneyDeserved,
      totalOrdersCompleted,
      totalOrdersAssigned,
      totalMoneyToCollect,
      totalMoneyCollected,
      lastUpdated,
    );
  }
}
