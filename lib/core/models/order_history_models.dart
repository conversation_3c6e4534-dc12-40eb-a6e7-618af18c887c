import 'package:myrunway/core/models/order_model_new.dart';
import 'package:myrunway/core/models/user_model.dart';
import 'package:myrunway/core/models/office_model.dart';

// Proof type enum based on API
enum ProofType { proofOfAssignment, proofOfDelivery, proofOfReturn }

extension ProofTypeExtension on ProofType {
  String get name {
    switch (this) {
      case ProofType.proofOfAssignment:
        return 'PROOF_OF_ASSIGNMENT';
      case ProofType.proofOfDelivery:
        return 'PROOF_OF_DELIVERY';
      case ProofType.proofOfReturn:
        return 'PROOF_OF_RETURN';
    }
  }

  String get displayName {
    switch (this) {
      case ProofType.proofOfAssignment:
        return 'إثبات التعيين';
      case ProofType.proofOfDelivery:
        return 'إثبات التسليم';
      case ProofType.proofOfReturn:
        return 'إثبات الإرجاع';
    }
  }

  static ProofType fromString(String value) {
    switch (value) {
      case 'PROOF_OF_ASSIGNMENT':
        return ProofType.proofOfAssignment;
      case 'PROOF_OF_DELIVERY':
        return ProofType.proofOfDelivery;
      case 'PROOF_OF_RETURN':
        return ProofType.proofOfReturn;
      default:
        return ProofType.proofOfAssignment;
    }
  }
}

// Order Assignment History model
class OrderAssignmentHistory {
  final int? id;
  final OfficeModel office;
  final OrderModelNew order;
  final UserModel assignedTo;
  final UserModel assignedBy;
  final DateTime assignedAt;

  OrderAssignmentHistory({
    this.id,
    required this.office,
    required this.order,
    required this.assignedTo,
    required this.assignedBy,
    required this.assignedAt,
  });

  factory OrderAssignmentHistory.fromJson(Map<String, dynamic> json) {
    return OrderAssignmentHistory(
      id: json['id'],
      office: OfficeModel.fromJson(json['office']),
      order: OrderModelNew.fromJson(json['order']),
      assignedTo: UserModel.fromJson(json['assigned_to']),
      assignedBy: UserModel.fromJson(json['assigned_by']),
      assignedAt: DateTime.parse(json['assigned_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'office': office.toJson(),
      'order': order.toJson(),
      'assigned_to': assignedTo.toJson(),
      'assigned_by': assignedBy.toJson(),
      'assigned_at': assignedAt.toIso8601String(),
    };
  }
}

// Order Handling Status History model
class OrderHandlingStatusHistory {
  final int? id;
  final OfficeModel office;
  final OrderModelNew order;
  final OrderHandlingStatus? handlingStatus;
  final OrderDeliveryStatus? deliveryStatus;
  final UserModel changedBy;
  final String? note;
  final OrderProof? proof;
  final DateTime createdAt;
  final DateTime updatedAt;

  OrderHandlingStatusHistory({
    this.id,
    required this.office,
    required this.order,
    this.handlingStatus,
    this.deliveryStatus,
    required this.changedBy,
    this.note,
    this.proof,
    required this.createdAt,
    required this.updatedAt,
  });

  factory OrderHandlingStatusHistory.fromJson(Map<String, dynamic> json) {
    return OrderHandlingStatusHistory(
      id: json['id'],
      office: OfficeModel.fromJson(json['office']),
      order: OrderModelNew.fromJson(json['order']),
      handlingStatus:
          json['handling_status'] != null
              ? OrderHandlingStatusExtension.fromString(json['handling_status'])
              : null,
      deliveryStatus:
          json['delivery_status'] != null
              ? OrderDeliveryStatus.fromJson(json['delivery_status'])
              : null,
      changedBy: UserModel.fromJson(json['changed_by']),
      note: json['note'],
      proof: json['proof'] != null ? OrderProof.fromJson(json['proof']) : null,
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'office': office.toJson(),
      'order': order.toJson(),
      'handling_status': handlingStatus?.name,
      'delivery_status': deliveryStatus?.toJson(),
      'changed_by': changedBy.toJson(),
      'note': note,
      'proof': proof?.toJson(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
}

// Order Proof model
class OrderProof {
  final int? id;
  final OrderModelNew order;
  final UserModel proofBy;
  final ProofType proofType;
  final String? proofImg;
  final DateTime createdAt;
  final DateTime updatedAt;
  final double? latitude;
  final double? longitude;

  OrderProof({
    this.id,
    required this.order,
    required this.proofBy,
    required this.proofType,
    this.proofImg,
    required this.createdAt,
    required this.updatedAt,
    this.latitude,
    this.longitude,
  });

  factory OrderProof.fromJson(Map<String, dynamic> json) {
    return OrderProof(
      id: json['id'],
      order: OrderModelNew.fromJson(json['order']),
      proofBy: UserModel.fromJson(json['proof_by']),
      proofType: ProofTypeExtension.fromString(json['proof_type']),
      proofImg: json['proof_img'],
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
      latitude: json['latitude']?.toDouble(),
      longitude: json['longitude']?.toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'order': order.toJson(),
      'proof_by': proofBy.toJson(),
      'proof_type': proofType.name,
      'proof_img': proofImg,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'latitude': latitude,
      'longitude': longitude,
    };
  }

  bool get hasLocation => latitude != null && longitude != null;
  bool get hasImage => proofImg != null && proofImg!.isNotEmpty;
}

// Order with History model (main response from API)
class OrderWithHistory {
  final OrderModelNew order;
  final List<OrderAssignmentHistory> assigningHistory;
  final List<OrderHandlingStatusHistory> handlingStatusHistory;
  final List<OrderProof> proofs;

  OrderWithHistory({
    required this.order,
    required this.assigningHistory,
    required this.handlingStatusHistory,
    required this.proofs,
  });

  factory OrderWithHistory.fromJson(Map<String, dynamic> json) {
    return OrderWithHistory(
      order: OrderModelNew.fromJson(json['order']),
      assigningHistory:
          (json['assigning_history'] as List)
              .map((item) => OrderAssignmentHistory.fromJson(item))
              .toList(),
      handlingStatusHistory:
          (json['handling_status_history'] as List)
              .map((item) => OrderHandlingStatusHistory.fromJson(item))
              .toList(),
      proofs:
          (json['proofs'] as List)
              .map((item) => OrderProof.fromJson(item))
              .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'order': order.toJson(),
      'assigning_history':
          assigningHistory.map((item) => item.toJson()).toList(),
      'handling_status_history':
          handlingStatusHistory.map((item) => item.toJson()).toList(),
      'proofs': proofs.map((item) => item.toJson()).toList(),
    };
  }

  // Helper methods
  bool get hasAssignmentHistory => assigningHistory.isNotEmpty;
  bool get hasStatusHistory => handlingStatusHistory.isNotEmpty;
  bool get hasProofs => proofs.isNotEmpty;

  OrderAssignmentHistory? get latestAssignment =>
      assigningHistory.isNotEmpty ? assigningHistory.last : null;

  OrderHandlingStatusHistory? get latestStatusChange =>
      handlingStatusHistory.isNotEmpty ? handlingStatusHistory.last : null;

  // Copy with method for updating the order
  OrderWithHistory copyWith({
    OrderModelNew? order,
    List<OrderAssignmentHistory>? assigningHistory,
    List<OrderHandlingStatusHistory>? handlingStatusHistory,
    List<OrderProof>? proofs,
  }) {
    return OrderWithHistory(
      order: order ?? this.order,
      assigningHistory: assigningHistory ?? this.assigningHistory,
      handlingStatusHistory:
          handlingStatusHistory ?? this.handlingStatusHistory,
      proofs: proofs ?? this.proofs,
    );
  }

  OrderProof? get latestProof => proofs.isNotEmpty ? proofs.last : null;
}
