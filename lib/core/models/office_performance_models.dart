class OfficePerformanceModel {
  final int totalOrders;
  final Map<String, int> ordersByStatus;
  final Map<String, int> ordersByDeliveryStatus;
  final double totalRevenue;
  final double averageOrderValue;
  final double collectionRate;
  final double? averageCompletionTime;
  final double onTimeDeliveryRate;
  final List<CompanyPerformanceModel> ordersByCompany;
  final List<EmployeePerformanceModel> topPerformingEmployees;
  final List<DailyPerformanceModel> dailyPerformance;

  OfficePerformanceModel({
    required this.totalOrders,
    required this.ordersByStatus,
    required this.ordersByDeliveryStatus,
    required this.totalRevenue,
    required this.averageOrderValue,
    required this.collectionRate,
    this.averageCompletionTime,
    required this.onTimeDeliveryRate,
    required this.ordersByCompany,
    required this.topPerformingEmployees,
    required this.dailyPerformance,
  });

  factory OfficePerformanceModel.fromJson(Map<String, dynamic> json) {
    return OfficePerformanceModel(
      totalOrders: json['total_orders'] ?? 0,
      ordersByStatus: Map<String, int>.from(json['orders_by_status'] ?? {}),
      ordersByDeliveryStatus: Map<String, int>.from(json['orders_by_delivery_status'] ?? {}),
      totalRevenue: (json['total_revenue'] ?? 0.0).toDouble(),
      averageOrderValue: (json['average_order_value'] ?? 0.0).toDouble(),
      collectionRate: (json['collection_rate'] ?? 0.0).toDouble(),
      averageCompletionTime: json['average_completion_time']?.toDouble(),
      onTimeDeliveryRate: (json['on_time_delivery_rate'] ?? 0.0).toDouble(),
      ordersByCompany: (json['orders_by_company'] as List<dynamic>? ?? [])
          .map((item) => CompanyPerformanceModel.fromJson(item as Map<String, dynamic>))
          .toList(),
      topPerformingEmployees: (json['top_performing_employees'] as List<dynamic>? ?? [])
          .map((item) => EmployeePerformanceModel.fromJson(item as Map<String, dynamic>))
          .toList(),
      dailyPerformance: (json['daily_performance'] as List<dynamic>? ?? [])
          .map((item) => DailyPerformanceModel.fromJson(item as Map<String, dynamic>))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'total_orders': totalOrders,
      'orders_by_status': ordersByStatus,
      'orders_by_delivery_status': ordersByDeliveryStatus,
      'total_revenue': totalRevenue,
      'average_order_value': averageOrderValue,
      'collection_rate': collectionRate,
      'average_completion_time': averageCompletionTime,
      'on_time_delivery_rate': onTimeDeliveryRate,
      'orders_by_company': ordersByCompany.map((item) => item.toJson()).toList(),
      'top_performing_employees': topPerformingEmployees.map((item) => item.toJson()).toList(),
      'daily_performance': dailyPerformance.map((item) => item.toJson()).toList(),
    };
  }
}

class CompanyPerformanceModel {
  final String companyName;
  final int orderCount;
  final double totalRevenue;

  CompanyPerformanceModel({
    required this.companyName,
    required this.orderCount,
    required this.totalRevenue,
  });

  factory CompanyPerformanceModel.fromJson(Map<String, dynamic> json) {
    return CompanyPerformanceModel(
      companyName: json['company_name'] ?? '',
      orderCount: json['order_count'] ?? 0,
      totalRevenue: (json['total_revenue'] ?? 0.0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'company_name': companyName,
      'order_count': orderCount,
      'total_revenue': totalRevenue,
    };
  }
}

class EmployeePerformanceModel {
  final String employeeName;
  final int ordersCompleted;
  final double totalRevenue;
  final double completionRate;

  EmployeePerformanceModel({
    required this.employeeName,
    required this.ordersCompleted,
    required this.totalRevenue,
    required this.completionRate,
  });

  factory EmployeePerformanceModel.fromJson(Map<String, dynamic> json) {
    return EmployeePerformanceModel(
      employeeName: json['employee_name'] ?? '',
      ordersCompleted: json['orders_completed'] ?? 0,
      totalRevenue: (json['total_revenue'] ?? 0.0).toDouble(),
      completionRate: (json['completion_rate'] ?? 0.0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'employee_name': employeeName,
      'orders_completed': ordersCompleted,
      'total_revenue': totalRevenue,
      'completion_rate': completionRate,
    };
  }
}

class DailyPerformanceModel {
  final DateTime date;
  final int ordersCount;
  final double revenue;
  final double completionRate;

  DailyPerformanceModel({
    required this.date,
    required this.ordersCount,
    required this.revenue,
    required this.completionRate,
  });

  factory DailyPerformanceModel.fromJson(Map<String, dynamic> json) {
    return DailyPerformanceModel(
      date: DateTime.parse(json['date']),
      ordersCount: json['orders_count'] ?? 0,
      revenue: (json['revenue'] ?? 0.0).toDouble(),
      completionRate: (json['completion_rate'] ?? 0.0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'date': date.toIso8601String(),
      'orders_count': ordersCount,
      'revenue': revenue,
      'completion_rate': completionRate,
    };
  }
}

class DateRangeModel {
  final DateTime startDate;
  final DateTime endDate;

  DateRangeModel({
    required this.startDate,
    required this.endDate,
  });

  Map<String, dynamic> toJson() {
    return {
      'start_date': startDate.toIso8601String().split('T')[0], // Format as YYYY-MM-DD
      'end_date': endDate.toIso8601String().split('T')[0],
    };
  }
}
