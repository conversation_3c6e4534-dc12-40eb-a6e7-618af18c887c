import 'package:myrunway/core/models/office_model.dart';
import 'package:myrunway/core/models/company_model.dart';

class CompanyChannelModel {
  final int? id;
  final OfficeModel office;
  final CompanyModel company;
  final String name;
  final String? notes;
  final String channelWhatsappNumber;
  final DateTime createdAt;
  final DateTime updatedAt;

  CompanyChannelModel({
    this.id,
    required this.office,
    required this.company,
    required this.name,
    this.notes,
    required this.channelWhatsappNumber,
    required this.createdAt,
    required this.updatedAt,
  });

  factory CompanyChannelModel.fromJson(Map<String, dynamic> json) {
    return CompanyChannelModel(
      id: json['id'],
      office: OfficeModel.fromJson(json['office']),
      company: CompanyModel.fromJson(json['company']),
      name: json['name'] ?? '',
      notes: json['notes'],
      channelWhatsappNumber: json['channel_whatsapp_number'] ?? '',
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'office': office.toJson(),
      'company': company.toJson(),
      'name': name,
      'notes': notes,
      'channel_whatsapp_number': channelWhatsappNumber,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  CompanyChannelModel copyWith({
    int? id,
    OfficeModel? office,
    CompanyModel? company,
    String? name,
    String? notes,
    String? channelWhatsappNumber,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return CompanyChannelModel(
      id: id ?? this.id,
      office: office ?? this.office,
      company: company ?? this.company,
      name: name ?? this.name,
      notes: notes ?? this.notes,
      channelWhatsappNumber: channelWhatsappNumber ?? this.channelWhatsappNumber,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'CompanyChannelModel(id: $id, name: $name, company: ${company.name}, whatsapp: $channelWhatsappNumber)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CompanyChannelModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

// Request model for creating a company channel
class CompanyChannelCreateRequest {
  final int companyId;
  final String name;
  final String? notes;
  final String channelWhatsappNumber;

  CompanyChannelCreateRequest({
    required this.companyId,
    required this.name,
    this.notes,
    required this.channelWhatsappNumber,
  });

  Map<String, dynamic> toJson() {
    return {
      'company_id': companyId,
      'name': name,
      'notes': notes,
      'channel_whatsapp_number': channelWhatsappNumber,
    };
  }

  // Validation method
  bool get isValid {
    return name.trim().length >= 3 && 
           channelWhatsappNumber.trim().length >= 10 &&
           companyId > 0;
  }

  String? get validationError {
    if (name.trim().length < 3) {
      return 'اسم القناة يجب أن يكون 3 أحرف على الأقل';
    }
    if (channelWhatsappNumber.trim().length < 10) {
      return 'رقم الواتساب يجب أن يكون 10 أرقام على الأقل';
    }
    if (companyId <= 0) {
      return 'يجب اختيار شركة صحيحة';
    }
    return null;
  }
}

// Request model for editing a company channel
class CompanyChannelEditRequest {
  final int? companyId;
  final String? name;
  final String? notes;
  final String? channelWhatsappNumber;

  CompanyChannelEditRequest({
    this.companyId,
    this.name,
    this.notes,
    this.channelWhatsappNumber,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    
    if (companyId != null) data['company_id'] = companyId;
    if (name != null && name!.trim().isNotEmpty) data['name'] = name!.trim();
    if (notes != null) data['notes'] = notes;
    if (channelWhatsappNumber != null && channelWhatsappNumber!.trim().isNotEmpty) {
      data['channel_whatsapp_number'] = channelWhatsappNumber!.trim();
    }
    
    return data;
  }

  // Check if request has any data to update
  bool get isEmpty {
    return companyId == null && 
           (name == null || name!.trim().isEmpty) &&
           notes == null &&
           (channelWhatsappNumber == null || channelWhatsappNumber!.trim().isEmpty);
  }

  // Validation method
  String? get validationError {
    if (name != null && name!.trim().length < 3) {
      return 'اسم القناة يجب أن يكون 3 أحرف على الأقل';
    }
    if (channelWhatsappNumber != null && channelWhatsappNumber!.trim().length < 10) {
      return 'رقم الواتساب يجب أن يكون 10 أرقام على الأقل';
    }
    if (companyId != null && companyId! <= 0) {
      return 'يجب اختيار شركة صحيحة';
    }
    return null;
  }
}
