/// QR Code generation request model
class QRCodeGenerateRequest {
  final int employeeId;

  QRCodeGenerateRequest({
    required this.employeeId,
  });

  Map<String, dynamic> toJson() {
    return {
      'employee_id': employeeId,
    };
  }
}

/// QR Code generation response model
class QRCodeGenerateResponse {
  final String qrToken;

  QRCodeGenerateResponse({
    required this.qrToken,
  });

  factory QRCodeGenerateResponse.fromJson(Map<String, dynamic> json) {
    return QRCodeGenerateResponse(
      qrToken: json['qr_token'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'qr_token': qrToken,
    };
  }
}

/// QR Code login request model
class QRCodeLoginRequest {
  final String qrToken;

  QRCodeLoginRequest({
    required this.qrToken,
  });

  Map<String, dynamic> toJson() {
    return {
      'qr_token': qrToken,
    };
  }
}
