import 'package:myrunway/core/models/office_model.dart';

class CompanyModel {
  final int? id;
  final OfficeModel office;
  final String name;
  final String? address;
  final String? phone;
  final String? colorCode;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  CompanyModel({
    this.id,
    required this.office,
    required this.name,
    this.address,
    this.phone,
    this.colorCode,
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
  });

  factory CompanyModel.fromJson(Map<String, dynamic> json) {
    return CompanyModel(
      id: json['id'],
      office:
          json['office'] is num
              ? OfficeModel(id: json['office'], name: '')
              : OfficeModel.fromJson(json['office']),
      name: json['name'] ?? '',
      address: json['address'],
      phone: json['phone'],
      colorCode: json['color_code'],
      isActive: json['is_active'] ?? true,
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'office': office.toJson(),
      'name': name,
      'address': address,
      'phone': phone,
      'color_code': colorCode,
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  Map<String, dynamic> toCreateJson() {
    return {
      'name': name,
      'address': address,
      'phone': phone,
      'color_code': colorCode,
    };
  }

  Map<String, dynamic> toEditJson() {
    final Map<String, dynamic> data = {};

    if (name.isNotEmpty) data['name'] = name;
    if (address != null && address!.isNotEmpty) data['address'] = address;
    if (phone != null && phone!.isNotEmpty) data['phone'] = phone;
    if (colorCode != null && colorCode!.isNotEmpty) {
      data['color_code'] = colorCode;
    }

    return data;
  }

  CompanyModel copyWith({
    int? id,
    OfficeModel? office,
    String? name,
    String? address,
    String? phone,
    String? colorCode,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return CompanyModel(
      id: id ?? this.id,
      office: office ?? this.office,
      name: name ?? this.name,
      address: address ?? this.address,
      phone: phone ?? this.phone,
      colorCode: colorCode ?? this.colorCode,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'CompanyModel(id: $id, name: $name, office: ${office.name})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CompanyModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

// Input schemas for API requests
class CompanyCreateRequest {
  final String name;
  final String? address;
  final String phone;
  final String colorCode;

  CompanyCreateRequest({
    required this.name,
    this.address,
    required this.phone,
    required this.colorCode,
  });

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'address': address,
      'phone': phone,
      'color_code': colorCode,
    };
  }
}

class CompanyEditRequest {
  final String? name;
  final String? address;
  final String? phone;
  final String? colorCode;

  CompanyEditRequest({this.name, this.address, this.phone, this.colorCode});

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};

    if (name != null) data['name'] = name;
    if (address != null) data['address'] = address;
    if (phone != null) data['phone'] = phone;
    if (colorCode != null) data['color_code'] = colorCode;

    return data;
  }

  bool get isEmpty =>
      name == null && address == null && phone == null && colorCode == null;
}
