import 'package:myrunway/core/models/user_model.dart';
import 'package:myrunway/core/models/order_model_new.dart';

// Transaction Type Enum based on OpenAPI schema
enum TransactionType {
  deposit('DEPOSIT', 'إيداع'),
  withdraw('WITHDRAW', 'سحب'),
  transfer('TRANSFER', 'تحويل'),
  commission('COMMISSION', 'عمولة'),
  refund('REFUND', 'استرداد'),
  returnType('RETURN', 'إرجاع'),
  other('OTHER', 'أخرى'),
  salary('SALARY', 'راتب'),
  settlement('SETTLEMENT', 'تسوية');

  const TransactionType(this.value, this.arabicName);
  final String value;
  final String arabicName;

  static TransactionType fromString(String value) {
    return TransactionType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => TransactionType.other,
    );
  }
}

// Employee Balance Transaction Model based on OpenAPI schema
class EmployeeBalanceTransactionModel {
  final int? id;
  final UserModel employee;
  final double amount;
  final TransactionType transactionType;
  final double balanceBefore;
  final UserModel createdBy;
  final OrderModelNew? order;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  EmployeeBalanceTransactionModel({
    this.id,
    required this.employee,
    required this.amount,
    required this.transactionType,
    required this.balanceBefore,
    required this.createdBy,
    this.order,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  factory EmployeeBalanceTransactionModel.fromJson(Map<String, dynamic> json) {
    return EmployeeBalanceTransactionModel(
      id: json['id'],
      employee: UserModel.fromJson(json['employee'] as Map<String, dynamic>),
      amount: _parseAmount(json['amount']),
      transactionType: TransactionType.fromString(json['transaction_type']),
      balanceBefore: _parseAmount(json['balance_before']),
      createdBy: UserModel.fromJson(json['created_by'] as Map<String, dynamic>),
      order: json['order'] != null 
          ? OrderModelNew.fromJson(json['order'] as Map<String, dynamic>)
          : null,
      notes: json['notes'],
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'employee': employee.toJson(),
      'amount': amount,
      'transaction_type': transactionType.value,
      'balance_before': balanceBefore,
      'created_by': createdBy.toJson(),
      'order': order?.toJson(),
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  // Helper method to parse amount (can be number or string)
  static double _parseAmount(dynamic amount) {
    if (amount is double) return amount;
    if (amount is int) return amount.toDouble();
    if (amount is String) return double.tryParse(amount) ?? 0.0;
    return 0.0;
  }

  // Helper getters for formatted display
  String get formattedAmount {
    return '${amount.toStringAsFixed(2)} جنيه';
  }

  String get formattedBalanceBefore {
    return '${balanceBefore.toStringAsFixed(2)} جنيه';
  }

  double get balanceAfter {
    switch (transactionType) {
      case TransactionType.deposit:
      case TransactionType.commission:
      case TransactionType.salary:
      case TransactionType.refund:
        return balanceBefore + amount;
      case TransactionType.withdraw:
      case TransactionType.settlement:
      case TransactionType.transfer:
        return balanceBefore - amount;
      default:
        return balanceBefore;
    }
  }

  String get formattedBalanceAfter {
    return '${balanceAfter.toStringAsFixed(2)} جنيه';
  }

  String get formattedCreatedAt {
    return '${createdAt.day}/${createdAt.month}/${createdAt.year} ${createdAt.hour}:${createdAt.minute.toString().padLeft(2, '0')}';
  }

  String get transactionTypeArabic {
    return transactionType.arabicName;
  }

  // Get transaction color based on type
  String get transactionColor {
    switch (transactionType) {
      case TransactionType.deposit:
      case TransactionType.commission:
      case TransactionType.salary:
      case TransactionType.refund:
        return '#4CAF50'; // Green for positive transactions
      case TransactionType.withdraw:
      case TransactionType.settlement:
        return '#F44336'; // Red for negative transactions
      case TransactionType.transfer:
        return '#FF9800'; // Orange for transfers
      default:
        return '#9E9E9E'; // Grey for others
    }
  }

  // Check if transaction is positive (increases balance)
  bool get isPositive {
    switch (transactionType) {
      case TransactionType.deposit:
      case TransactionType.commission:
      case TransactionType.salary:
      case TransactionType.refund:
        return true;
      default:
        return false;
    }
  }

  // Get transaction icon based on type
  String get transactionIcon {
    switch (transactionType) {
      case TransactionType.deposit:
        return 'add_circle';
      case TransactionType.withdraw:
        return 'remove_circle';
      case TransactionType.transfer:
        return 'swap_horiz';
      case TransactionType.commission:
        return 'monetization_on';
      case TransactionType.refund:
        return 'undo';
      case TransactionType.returnType:
        return 'keyboard_return';
      case TransactionType.salary:
        return 'payment';
      case TransactionType.settlement:
        return 'account_balance';
      default:
        return 'help';
    }
  }

  @override
  String toString() {
    return 'EmployeeBalanceTransactionModel(id: $id, amount: $amount, type: ${transactionType.value}, employee: ${employee.fullName})';
  }
}

// Settlement Request Model for API calls
class EmployeeSettlementRequest {
  final String? notes;

  EmployeeSettlementRequest({this.notes});

  Map<String, dynamic> toJson() {
    return {
      if (notes != null) 'notes': notes,
    };
  }
}

// Balance Summary Model for displaying current balance info
class EmployeeBalanceSummary {
  final double currentBalance;
  final double totalEarned;
  final double totalWithdrawn;
  final double totalSettled;
  final DateTime? lastTransactionDate;
  final int totalTransactions;

  EmployeeBalanceSummary({
    required this.currentBalance,
    required this.totalEarned,
    required this.totalWithdrawn,
    required this.totalSettled,
    this.lastTransactionDate,
    required this.totalTransactions,
  });

  factory EmployeeBalanceSummary.fromTransactions(
    List<EmployeeBalanceTransactionModel> transactions,
  ) {
    if (transactions.isEmpty) {
      return EmployeeBalanceSummary(
        currentBalance: 0.0,
        totalEarned: 0.0,
        totalWithdrawn: 0.0,
        totalSettled: 0.0,
        totalTransactions: 0,
      );
    }

    // Sort transactions by date (newest first)
    final sortedTransactions = List<EmployeeBalanceTransactionModel>.from(transactions)
      ..sort((a, b) => b.createdAt.compareTo(a.createdAt));

    final currentBalance = sortedTransactions.first.balanceAfter;
    
    double totalEarned = 0.0;
    double totalWithdrawn = 0.0;
    double totalSettled = 0.0;

    for (final transaction in transactions) {
      switch (transaction.transactionType) {
        case TransactionType.deposit:
        case TransactionType.commission:
        case TransactionType.salary:
        case TransactionType.refund:
          totalEarned += transaction.amount;
          break;
        case TransactionType.withdraw:
          totalWithdrawn += transaction.amount;
          break;
        case TransactionType.settlement:
          totalSettled += transaction.amount;
          break;
        default:
          break;
      }
    }

    return EmployeeBalanceSummary(
      currentBalance: currentBalance,
      totalEarned: totalEarned,
      totalWithdrawn: totalWithdrawn,
      totalSettled: totalSettled,
      lastTransactionDate: sortedTransactions.first.createdAt,
      totalTransactions: transactions.length,
    );
  }

  // Helper getters for formatted display
  String get formattedCurrentBalance {
    return '${currentBalance.toStringAsFixed(2)} جنيه';
  }

  String get formattedTotalEarned {
    return '${totalEarned.toStringAsFixed(2)} جنيه';
  }

  String get formattedTotalWithdrawn {
    return '${totalWithdrawn.toStringAsFixed(2)} جنيه';
  }

  String get formattedTotalSettled {
    return '${totalSettled.toStringAsFixed(2)} جنيه';
  }

  String get formattedLastTransactionDate {
    if (lastTransactionDate == null) return 'لا توجد معاملات';
    final date = lastTransactionDate!;
    return '${date.day}/${date.month}/${date.year}';
  }

  @override
  String toString() {
    return 'EmployeeBalanceSummary(currentBalance: $currentBalance, totalTransactions: $totalTransactions)';
  }
}
