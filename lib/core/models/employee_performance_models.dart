class EmployeePerformanceModel {
  final int employeeId;
  final String employeeName;
  final int totalOrdersAssigned;
  final int totalOrdersCompleted;
  final double completionRate;
  final double? averageCompletionTime;
  final double totalRevenue;
  final double commissionEarned;
  final double averageOrderValue;
  final double onTimeDeliveryPercentage;
  final Map<String, int> ordersByStatus;
  final List<DailyPerformanceModel> dailyPerformance;

  EmployeePerformanceModel({
    required this.employeeId,
    required this.employeeName,
    required this.totalOrdersAssigned,
    required this.totalOrdersCompleted,
    required this.completionRate,
    this.averageCompletionTime,
    required this.totalRevenue,
    required this.commissionEarned,
    required this.averageOrderValue,
    required this.onTimeDeliveryPercentage,
    required this.ordersByStatus,
    required this.dailyPerformance,
  });

  factory EmployeePerformanceModel.fromJson(Map<String, dynamic> json) {
    return EmployeePerformanceModel(
      employeeId: json['employee_id'] ?? 0,
      employeeName: json['employee_name'] ?? '',
      totalOrdersAssigned: json['total_orders_assigned'] ?? 0,
      totalOrdersCompleted: json['total_orders_completed'] ?? 0,
      completionRate: (json['completion_rate'] ?? 0.0).toDouble(),
      averageCompletionTime: json['average_completion_time']?.toDouble(),
      totalRevenue: (json['total_revenue'] ?? 0.0).toDouble(),
      commissionEarned: (json['commission_earned'] ?? 0.0).toDouble(),
      averageOrderValue: (json['average_order_value'] ?? 0.0).toDouble(),
      onTimeDeliveryPercentage: (json['on_time_delivery_percentage'] ?? 0.0).toDouble(),
      ordersByStatus: Map<String, int>.from(json['orders_by_status'] ?? {}),
      dailyPerformance: (json['daily_performance'] as List<dynamic>? ?? [])
          .map((item) => DailyPerformanceModel.fromJson(item as Map<String, dynamic>))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'employee_id': employeeId,
      'employee_name': employeeName,
      'total_orders_assigned': totalOrdersAssigned,
      'total_orders_completed': totalOrdersCompleted,
      'completion_rate': completionRate,
      'average_completion_time': averageCompletionTime,
      'total_revenue': totalRevenue,
      'commission_earned': commissionEarned,
      'average_order_value': averageOrderValue,
      'on_time_delivery_percentage': onTimeDeliveryPercentage,
      'orders_by_status': ordersByStatus,
      'daily_performance': dailyPerformance.map((item) => item.toJson()).toList(),
    };
  }

  // Helper getters for formatted display
  String get formattedTotalRevenue {
    return '${totalRevenue.toStringAsFixed(2)} جنيه';
  }

  String get formattedCommissionEarned {
    return '${commissionEarned.toStringAsFixed(2)} جنيه';
  }

  String get formattedAverageOrderValue {
    return '${averageOrderValue.toStringAsFixed(2)} جنيه';
  }

  String get formattedCompletionRate {
    return '${(completionRate * 100).toStringAsFixed(1)}%';
  }

  String get formattedOnTimeDeliveryPercentage {
    return '${(onTimeDeliveryPercentage * 100).toStringAsFixed(1)}%';
  }

  String get formattedAverageCompletionTime {
    if (averageCompletionTime == null) return 'غير متاح';
    final hours = averageCompletionTime! / 60;
    if (hours < 1) {
      return '${averageCompletionTime!.toStringAsFixed(0)} دقيقة';
    } else if (hours < 24) {
      return '${hours.toStringAsFixed(1)} ساعة';
    } else {
      final days = hours / 24;
      return '${days.toStringAsFixed(1)} يوم';
    }
  }

  // Performance indicators
  bool get isHighPerformer => completionRate >= 0.9 && onTimeDeliveryPercentage >= 0.85;
  bool get needsImprovement => completionRate < 0.7 || onTimeDeliveryPercentage < 0.7;

  // Status breakdown helpers
  int get pendingOrders => ordersByStatus['pending'] ?? 0;
  int get assignedOrders => ordersByStatus['assigned'] ?? 0;
  int get processingOrders => ordersByStatus['processing'] ?? 0;
  int get completedOrders => ordersByStatus['completed'] ?? 0;

  // Performance trends (based on daily performance)
  double get recentPerformanceTrend {
    if (dailyPerformance.length < 2) return 0.0;
    
    final recent = dailyPerformance.take(7).toList();
    if (recent.length < 2) return 0.0;
    
    final recentAvg = recent.map((d) => d.completionRate).reduce((a, b) => a + b) / recent.length;
    final olderData = dailyPerformance.skip(7).take(7).toList();
    
    if (olderData.isEmpty) return 0.0;
    
    final olderAvg = olderData.map((d) => d.completionRate).reduce((a, b) => a + b) / olderData.length;
    
    return recentAvg - olderAvg;
  }

  // Create empty model for loading state
  factory EmployeePerformanceModel.empty() {
    return EmployeePerformanceModel(
      employeeId: 0,
      employeeName: '',
      totalOrdersAssigned: 0,
      totalOrdersCompleted: 0,
      completionRate: 0.0,
      averageCompletionTime: null,
      totalRevenue: 0.0,
      commissionEarned: 0.0,
      averageOrderValue: 0.0,
      onTimeDeliveryPercentage: 0.0,
      ordersByStatus: {},
      dailyPerformance: [],
    );
  }

  @override
  String toString() {
    return 'EmployeePerformanceModel(employeeId: $employeeId, employeeName: $employeeName, completionRate: $completionRate, totalRevenue: $totalRevenue)';
  }
}

class DailyPerformanceModel {
  final DateTime date;
  final int totalOrders;
  final int completedOrders;
  final double revenue;

  DailyPerformanceModel({
    required this.date,
    required this.totalOrders,
    required this.completedOrders,
    required this.revenue,
  });

  factory DailyPerformanceModel.fromJson(Map<String, dynamic> json) {
    return DailyPerformanceModel(
      date: DateTime.parse(json['date']),
      totalOrders: json['total_orders'] ?? 0,
      completedOrders: json['completed_orders'] ?? 0,
      revenue: (json['revenue'] ?? 0.0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'date': date.toIso8601String().split('T')[0], // Format as YYYY-MM-DD
      'total_orders': totalOrders,
      'completed_orders': completedOrders,
      'revenue': revenue,
    };
  }

  // Helper getters
  double get completionRate {
    if (totalOrders == 0) return 0.0;
    return completedOrders / totalOrders;
  }

  String get formattedDate {
    return '${date.day}/${date.month}/${date.year}';
  }

  String get formattedRevenue {
    return '${revenue.toStringAsFixed(2)} جنيه';
  }

  String get formattedCompletionRate {
    return '${(completionRate * 100).toStringAsFixed(1)}%';
  }

  @override
  String toString() {
    return 'DailyPerformanceModel(date: $date, totalOrders: $totalOrders, completedOrders: $completedOrders, revenue: $revenue)';
  }
}

// Date range model for API requests
class DateRangeModel {
  final DateTime startDate;
  final DateTime endDate;

  DateRangeModel({
    required this.startDate,
    required this.endDate,
  });

  Map<String, dynamic> toJson() {
    return {
      'start_date': startDate.toIso8601String().split('T')[0],
      'end_date': endDate.toIso8601String().split('T')[0],
    };
  }

  factory DateRangeModel.fromJson(Map<String, dynamic> json) {
    return DateRangeModel(
      startDate: DateTime.parse(json['start_date']),
      endDate: DateTime.parse(json['end_date']),
    );
  }

  // Helper methods for common date ranges
  static DateRangeModel lastWeek() {
    final now = DateTime.now();
    return DateRangeModel(
      startDate: now.subtract(const Duration(days: 7)),
      endDate: now,
    );
  }

  static DateRangeModel lastMonth() {
    final now = DateTime.now();
    return DateRangeModel(
      startDate: DateTime(now.year, now.month - 1, now.day),
      endDate: now,
    );
  }

  static DateRangeModel last30Days() {
    final now = DateTime.now();
    return DateRangeModel(
      startDate: now.subtract(const Duration(days: 30)),
      endDate: now,
    );
  }

  @override
  String toString() {
    return 'DateRangeModel(startDate: $startDate, endDate: $endDate)';
  }
}
