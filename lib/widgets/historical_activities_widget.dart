import 'package:flutter/material.dart';
import 'package:myrunway/core/models/historical_activity_models.dart';
import 'package:myrunway/core/constants/app_colors.dart';

class HistoricalActivitiesWidget extends StatelessWidget {
  final List<HistoricalActivityModel> activities;
  final bool isLoading;
  final String title;
  final VoidCallback? onRefresh;
  final bool showLoadMore;
  final VoidCallback? onLoadMore;
  final bool isLoadingMore;

  const HistoricalActivitiesWidget({
    super.key,
    required this.activities,
    this.isLoading = false,
    this.title = 'الأنشطة الأخيرة',
    this.onRefresh,
    this.showLoadMore = false,
    this.onLoadMore,
    this.isLoadingMore = false,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.timeline, color: AppColors.primary),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                if (onRefresh != null)
                  IconButton(
                    icon: const Icon(Icons.refresh),
                    onPressed: isLoading ? null : onRefresh,
                  ),
              ],
            ),
            Text(
              'يتم التحديث كل 5 دقائق',
              style: TextStyle(fontSize: 12, color: Colors.grey[600]),
            ),
            const SizedBox(height: 16),

            if (isLoading)
              const Center(
                child: Padding(
                  padding: EdgeInsets.all(32),
                  child: CircularProgressIndicator(),
                ),
              )
            else if (activities.isEmpty)
              const Center(
                child: Padding(
                  padding: EdgeInsets.all(32),
                  child: Text(
                    'لا توجد أنشطة حديثة',
                    style: TextStyle(color: Colors.grey),
                  ),
                ),
              )
            else
              Column(
                children: [
                  ListView.separated(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: activities.length,
                    separatorBuilder:
                        (context, index) => const SizedBox(height: 16),
                    itemBuilder: (context, index) {
                      final activity = activities[index];
                      final isLast =
                          index == activities.length - 1 && !showLoadMore;

                      return _HistoricalActivityItem(
                        activity: activity,
                        isLast: isLast,
                      );
                    },
                  ),
                  if (showLoadMore) ...[
                    const SizedBox(height: 16),
                    if (isLoadingMore)
                      const Center(child: CircularProgressIndicator())
                    else
                      Center(
                        child: TextButton(
                          onPressed: onLoadMore,
                          child: const Text('عرض المزيد'),
                        ),
                      ),
                  ],
                ],
              ),
          ],
        ),
      ),
    );
  }
}

class _HistoricalActivityItem extends StatelessWidget {
  final HistoricalActivityModel activity;
  final bool isLast;

  const _HistoricalActivityItem({required this.activity, required this.isLast});

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Timeline indicator
        Column(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: _getActivityColor(activity.activityType),
                shape: BoxShape.circle,
              ),
              child: Icon(
                _getActivityIcon(activity.activityType),
                color: Colors.white,
                size: 20,
              ),
            ),
            if (!isLast)
              Container(
                width: 2,
                height: 40,
                color: Colors.grey[300],
                margin: const EdgeInsets.only(top: 8),
              ),
          ],
        ),
        const SizedBox(width: 16),

        // Activity content
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                activity.description,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                'بواسطة ${activity.userName}',
                style: TextStyle(fontSize: 12, color: Colors.grey[600]),
              ),
              if (activity.orderCode != null) ...[
                const SizedBox(height: 4),
                Text(
                  'الطلب: ${activity.orderCode}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.blue[600],
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
              if (activity.customerName != null) ...[
                const SizedBox(height: 4),
                Text(
                  'العميل: ${activity.customerName}',
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
              ],
              const SizedBox(height: 4),
              Row(
                children: [
                  Icon(Icons.access_time, size: 14, color: Colors.grey[500]),
                  const SizedBox(width: 4),
                  Text(
                    _formatDateTime(activity.timestamp),
                    style: TextStyle(fontSize: 12, color: Colors.grey[500]),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Color _getActivityColor(String activityType) {
    switch (activityType.toLowerCase()) {
      case 'order_created':
      case 'created':
        return Colors.blue;
      case 'order_assigned':
      case 'assigned':
        return Colors.orange;
      case 'status_changed':
      case 'status_update':
        return Colors.purple;
      case 'order_delivered':
      case 'delivered':
        return Colors.green;
      case 'user_created':
      case 'user_added':
        return Colors.teal;
      case 'office_created':
        return Colors.indigo;
      default:
        return Colors.grey;
    }
  }

  IconData _getActivityIcon(String activityType) {
    switch (activityType.toLowerCase()) {
      case 'order_created':
        return Icons.add_circle;
      case 'created':
        return Icons.add_circle;
      case 'order_assignment':
        return Icons.person_add;
      case 'assigned':
        return Icons.person_add;
      case 'status_change':
        return Icons.update;
      case 'status_update':
        return Icons.update;
      case 'order_delivered':
        return Icons.check_circle;
      case 'delivered':
        return Icons.check_circle;
      case 'user_created':
        return Icons.person_add;
      case 'user_added':
        return Icons.person_add;
      case 'office_created':
        return Icons.business;
      default:
        return Icons.info;
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
