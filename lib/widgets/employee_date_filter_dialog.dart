import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:myrunway/core/constants/app_colors.dart';
import 'package:myrunway/pages/employee/home_controller.dart';

class EmployeeDateFilterDialog extends StatelessWidget {
  final EmployeeHomeController controller;

  const EmployeeDateFilterDialog({
    super.key,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'فلترة حسب التاريخ',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  onPressed: () => Get.back(),
                  icon: const Icon(Icons.close),
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                ),
              ],
            ),
            
            const SizedBox(height: 20),
            
            // Quick date filters
            const Text(
              'فترات زمنية سريعة',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                _buildQuickFilterChip('اليوم', controller.setTodayFilter),
                _buildQuickFilterChip('أمس', controller.setYesterdayFilter),
                _buildQuickFilterChip('غداً', controller.setTomorrowFilter),
                _buildQuickFilterChip('هذا الأسبوع', controller.setThisWeekFilter),
                _buildQuickFilterChip('الأسبوع الماضي', controller.setLastWeekFilter),
                _buildQuickFilterChip('هذا الشهر', controller.setThisMonthFilter),
                _buildQuickFilterChip('الشهر الماضي', controller.setLastMonthFilter),
              ],
            ),
            
            const SizedBox(height: 24),
            
            // Custom date range
            const Text(
              'نطاق تاريخ مخصص',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            
            Row(
              children: [
                Expanded(
                  child: InkWell(
                    onTap: () => _showDateRangePicker(context),
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 16,
                      ),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        children: [
                          Icon(Icons.date_range, color: Colors.grey[600]),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Obx(() {
                              return Text(
                                _getDateRangeText(),
                                style: TextStyle(
                                  color: controller.dateFrom.value != null || 
                                         controller.dateTo.value != null
                                      ? Colors.black
                                      : Colors.grey[600],
                                ),
                              );
                            }),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 24),
            
            // Action buttons
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () {
                      controller.clearFilters();
                      Get.back();
                    },
                    child: const Text('مسح الفلاتر'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => Get.back(),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('تطبيق'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickFilterChip(String label, VoidCallback onTap) {
    return Obx(() {
      final isActive = controller.isQuickFilterActive(label);
      return ActionChip(
        label: Text(label),
        onPressed: () {
          onTap();
          Get.back();
        },
        backgroundColor: isActive 
            ? AppColors.primary.withValues(alpha: 0.1) 
            : Colors.grey[100],
        labelStyle: TextStyle(
          fontSize: 12,
          color: isActive ? AppColors.primary : Colors.black87,
          fontWeight: isActive ? FontWeight.w600 : FontWeight.normal,
        ),
        side: BorderSide(
          color: isActive ? AppColors.primary : Colors.transparent,
          width: 1,
        ),
      );
    });
  }

  String _getDateRangeText() {
    if (controller.dateFrom.value != null && controller.dateTo.value != null) {
      final from = _formatDate(controller.dateFrom.value!);
      final to = _formatDate(controller.dateTo.value!);
      return '$from - $to';
    } else if (controller.dateFrom.value != null) {
      return 'من ${_formatDate(controller.dateFrom.value!)}';
    } else if (controller.dateTo.value != null) {
      return 'إلى ${_formatDate(controller.dateTo.value!)}';
    }
    return 'اختر نطاق التاريخ...';
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  Future<void> _showDateRangePicker(BuildContext context) async {
    final DateTimeRange? range = await showDateRangePicker(
      context: context,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      initialDateRange: controller.dateFrom.value != null && 
                       controller.dateTo.value != null
          ? DateTimeRange(
              start: controller.dateFrom.value!, 
              end: controller.dateTo.value!
            )
          : null,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: AppColors.primary,
              onPrimary: Colors.white,
              onSurface: Colors.black,
            ),
          ),
          child: child!,
        );
      },
    );

    if (range != null) {
      controller.setDateFilter(range.start, range.end);
    }
  }
}
