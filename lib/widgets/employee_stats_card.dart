import 'package:flutter/material.dart';
import 'package:myrunway/core/constants/app_colors.dart';
import 'package:myrunway/core/models/employee_stats_model.dart';

class EmployeeStatsCard extends StatelessWidget {
  final EmployeeStatsModel? stats;
  final bool isLoading;
  final VoidCallback? onFilterTap;

  const EmployeeStatsCard({
    super.key,
    this.stats,
    required this.isLoading,
    this.onFilterTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [AppColors.primary, AppColors.primaryLight],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withValues(alpha: 0.3),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with filter button
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'إحصائياتي',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              if (onFilterTap != null)
                IconButton(
                  onPressed: onFilterTap,
                  icon: const Icon(
                    Icons.filter_list,
                    color: Colors.white70,
                    size: 20,
                  ),
                  tooltip: 'فلترة حسب التاريخ',
                ),
            ],
          ),

          const SizedBox(height: 20),

          if (isLoading)
            _buildLoadingState()
          else if (stats != null)
            _buildStatsContent()
          else
            _buildEmptyState(),
        ],
      ),
    );
  }

  Widget _buildLoadingState() {
    return const Column(
      children: [
        Center(
          child: CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
          ),
        ),
        SizedBox(height: 16),
        Text(
          'جاري تحميل الإحصائيات...',
          style: TextStyle(fontSize: 14, color: Colors.white70),
        ),
      ],
    );
  }

  Widget _buildEmptyState() {
    return const Column(
      children: [
        Icon(Icons.analytics_outlined, size: 48, color: Colors.white70),
        SizedBox(height: 16),
        Text(
          'لا توجد إحصائيات متاحة',
          style: TextStyle(fontSize: 16, color: Colors.white70),
        ),
      ],
    );
  }

  Widget _buildStatsContent() {
    return Column(
      children: [
        // Main stats row
        Row(
          children: [
            Expanded(
              child: _buildStatItem(
                'الطلبات المكتملة',
                stats!.totalOrdersCompleted.toString(),
                Icons.check_circle_outline,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildStatItem(
                'إجمالي العمولة',
                stats!.formattedTotalMoneyDeserved,
                Icons.monetization_on_outlined,
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // Money owed section
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  const Icon(
                    Icons.account_balance_wallet_outlined,
                    color: Colors.white70,
                    size: 16,
                  ),
                  const SizedBox(width: 8),
                  const Text(
                    'المبلغ المستحق',
                    style: TextStyle(fontSize: 14, color: Colors.white70),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                stats!.formattedTotalMoneyToCollect,
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              // show a hint with current total money collected
              Text(
                'المبلغ المجمع حتى الآن: ${stats!.formattedTotalMoneyCollected}',
                style: const TextStyle(fontSize: 14, color: Colors.white70),
              ),
            ],
          ),
        ),

        const SizedBox(height: 16),

        // Additional info
        Row(
          children: [
            const Icon(Icons.assignment, color: Colors.white70, size: 16),
            const SizedBox(width: 8),
            Text(
              'إجمالي ${stats!.totalOrdersAssigned} طلب معين',
              style: const TextStyle(fontSize: 14, color: Colors.white70),
            ),
            const SizedBox(width: 16),
            const Icon(Icons.trending_up, color: Colors.white70, size: 16),
            const SizedBox(width: 8),
            Text(
              'معدل الإنجاز ${stats!.formattedCompletionRate}',
              style: const TextStyle(fontSize: 14, color: Colors.white70),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatItem(String title, String value, IconData icon) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, color: Colors.white70, size: 16),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                title,
                style: const TextStyle(fontSize: 14, color: Colors.white70),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
      ],
    );
  }
}
