import 'package:flutter/material.dart';

/// Reusable filter container widget with consistent styling
class FilterContainerWidget extends StatelessWidget {
  final String title;
  final List<Widget> children;
  final VoidCallback? onClearFilters;
  final String clearFiltersText;
  final bool showClearButton;
  final EdgeInsets? padding;
  final BorderRadius? borderRadius;

  const FilterContainerWidget({
    super.key,
    required this.title,
    required this.children,
    this.onClearFilters,
    this.clearFiltersText = 'مسح الفلاتر',
    this.showClearButton = true,
    this.padding,
    this.borderRadius,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: padding ?? const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: borderRadius ?? BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Filter Header
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title,
                style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              if (showClearButton && onClearFilters != null)
                TextButton.icon(
                  onPressed: onClearFilters,
                  icon: const Icon(Icons.clear, size: 16),
                  label: Text(clearFiltersText),
                  style: TextButton.styleFrom(foregroundColor: Colors.blue),
                ),
            ],
          ),
          const SizedBox(height: 12),
          
          // Filter Content
          ...children,
        ],
      ),
    );
  }
}

/// Reusable filter section widget for organizing filters
class FilterSectionWidget extends StatelessWidget {
  final String title;
  final Widget child;
  final EdgeInsets? padding;

  const FilterSectionWidget({
    super.key,
    required this.title,
    required this.child,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding ?? const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: 8),
          child,
        ],
      ),
    );
  }
}

/// Reusable filter chip widget with consistent styling
class FilterChipWidget extends StatelessWidget {
  final String label;
  final bool isActive;
  final VoidCallback onPressed;
  final Color? activeColor;
  final Color? inactiveColor;
  final TextStyle? activeTextStyle;
  final TextStyle? inactiveTextStyle;

  const FilterChipWidget({
    super.key,
    required this.label,
    required this.isActive,
    required this.onPressed,
    this.activeColor,
    this.inactiveColor,
    this.activeTextStyle,
    this.inactiveTextStyle,
  });

  @override
  Widget build(BuildContext context) {
    return ActionChip(
      label: Text(label),
      onPressed: onPressed,
      backgroundColor: isActive 
          ? (activeColor ?? Colors.blue.withValues(alpha: 0.1))
          : (inactiveColor ?? Colors.grey[100]),
      labelStyle: isActive
          ? (activeTextStyle ?? const TextStyle(
              fontSize: 12,
              color: Colors.blue,
              fontWeight: FontWeight.w600,
            ))
          : (inactiveTextStyle ?? const TextStyle(
              fontSize: 12,
              color: Colors.black87,
              fontWeight: FontWeight.normal,
            )),
      side: BorderSide(
        color: isActive ? Colors.blue : Colors.transparent,
        width: 1,
      ),
    );
  }
}

/// Filter configuration class for consistent filter behavior
class FilterConfig {
  final String title;
  final bool showClearButton;
  final String clearFiltersText;
  final EdgeInsets padding;
  final BorderRadius borderRadius;

  const FilterConfig({
    required this.title,
    this.showClearButton = true,
    this.clearFiltersText = 'مسح الفلاتر',
    this.padding = const EdgeInsets.all(16),
    this.borderRadius = const BorderRadius.all(Radius.circular(12)),
  });
}

/// Filter state management helper
abstract class FilterState {
  bool get hasActiveFilters;
  void clearAllFilters();
  void applyFilters();
}

/// Date filter state
class DateFilterState {
  DateTime? dateFrom;
  DateTime? dateTo;
  String activeQuickFilter;

  DateFilterState({
    this.dateFrom,
    this.dateTo,
    this.activeQuickFilter = '',
  });

  bool get hasDateFilter => dateFrom != null || dateTo != null;
  
  void clearDateFilter() {
    dateFrom = null;
    dateTo = null;
    activeQuickFilter = '';
  }

  void setDateRange(DateTime from, DateTime to, String filterName) {
    dateFrom = from;
    dateTo = to;
    activeQuickFilter = filterName;
  }
}
