import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// Reusable date filter widget with quick filter buttons and custom date range picker
class DateFilterWidget extends StatelessWidget {
  final String title;
  final VoidCallback onTodayTap;
  final VoidCallback onYesterdayTap;
  final VoidCallback onTomorrowTap;
  final VoidCallback onThisWeekTap;
  final VoidCallback onLastWeekTap;
  final VoidCallback onThisMonthTap;
  final VoidCallback onLastMonthTap;
  final VoidCallback onCustomDateTap;
  final bool Function(String) isQuickFilterActive;
  final bool hasCustomRange;

  const DateFilterWidget({
    super.key,
    required this.title,
    required this.onTodayTap,
    required this.onYesterdayTap,
    required this.onTomorrowTap,
    required this.onThisWeekTap,
    required this.onLastWeekTap,
    required this.onThisMonthTap,
    required this.onLastMonthTap,
    required this.onCustomDateTap,
    required this.isQuickFilterActive,
    required this.hasCustomRange,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            _buildQuickFilterChip('اليوم', onTodayTap),
            _buildQuickFilterChip('أمس', onYesterdayTap),
            _buildQuickFilterChip('غداً', onTomorrowTap),
            _buildQuickFilterChip('هذا الأسبوع', onThisWeekTap),
            _buildQuickFilterChip('الأسبوع الماضي', onLastWeekTap),
            _buildQuickFilterChip('هذا الشهر', onThisMonthTap),
            _buildQuickFilterChip('الشهر الماضي', onLastMonthTap),
            _buildCustomDateFilterChip(),
          ],
        ),
      ],
    );
  }

  Widget _buildQuickFilterChip(String label, VoidCallback onTap) {
    final isActive = isQuickFilterActive(label);
    return ActionChip(
      label: Text(label),
      onPressed: onTap,
      backgroundColor:
          isActive ? Colors.blue.withValues(alpha: 0.1) : Colors.grey[100],
      labelStyle: TextStyle(
        fontSize: 12,
        color: isActive ? Colors.blue : Colors.black87,
        fontWeight: isActive ? FontWeight.w600 : FontWeight.normal,
      ),
      side: BorderSide(
        color: isActive ? Colors.blue : Colors.transparent,
        width: 1,
      ),
    );
  }

  Widget _buildCustomDateFilterChip() {
    return ActionChip(
      label: Text(hasCustomRange ? 'نطاق مخصص' : 'تاريخ مخصص'),
      onPressed: onCustomDateTap,
      backgroundColor:
          hasCustomRange
              ? Colors.blue.withValues(alpha: 0.1)
              : Colors.grey[100],
      labelStyle: TextStyle(
        fontSize: 12,
        color: hasCustomRange ? Colors.blue : Colors.black87,
        fontWeight: hasCustomRange ? FontWeight.w600 : FontWeight.normal,
      ),
      side: BorderSide(
        color: hasCustomRange ? Colors.blue : Colors.transparent,
        width: 1,
      ),
    );
  }
}

/// Reusable date range picker dialog
class DateRangePickerDialog extends StatelessWidget {
  final String title;
  final DateTime? dateFrom;
  final DateTime? dateTo;
  final Function(DateTime from, DateTime to) onDateRangeSelected;

  const DateRangePickerDialog({
    super.key,
    required this.title,
    this.dateFrom,
    this.dateTo,
    required this.onDateRangeSelected,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            _buildDateRangePicker(context),
            const SizedBox(height: 24),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => Get.back(),
                  child: const Text('إلغاء'),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: () => Get.back(),
                  child: const Text('تطبيق'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDateRangePicker(BuildContext context) {
    return InkWell(
      onTap: () => _showDateRangePicker(context),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Icon(Icons.date_range, color: Colors.grey[600]),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                _getDateRangeText(),
                style: TextStyle(
                  color: dateFrom != null || dateTo != null
                      ? Colors.black
                      : Colors.grey[600],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _showDateRangePicker(BuildContext context) async {
    final DateTimeRange? range = await showDateRangePicker(
      context: context,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      initialDateRange: dateFrom != null && dateTo != null
          ? DateTimeRange(start: dateFrom!, end: dateTo!)
          : null,
    );

    if (range != null) {
      onDateRangeSelected(range.start, range.end);
    }
  }

  String _getDateRangeText() {
    if (dateFrom != null && dateTo != null) {
      return '${_formatDate(dateFrom!)} - ${_formatDate(dateTo!)}';
    } else if (dateFrom != null) {
      return 'من ${_formatDate(dateFrom!)}';
    } else if (dateTo != null) {
      return 'إلى ${_formatDate(dateTo!)}';
    }
    return 'اختر نطاق التاريخ';
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}

/// Utility class for date filter operations
class DateFilterUtils {
  static DateTime getStartOfDay(DateTime date) {
    return DateTime(date.year, date.month, date.day);
  }

  static DateTime getEndOfDay(DateTime date) {
    return DateTime(date.year, date.month, date.day, 23, 59, 59);
  }

  static DateTimeRange getTodayRange() {
    final today = DateTime.now();
    return DateTimeRange(
      start: getStartOfDay(today),
      end: getEndOfDay(today),
    );
  }

  static DateTimeRange getYesterdayRange() {
    final yesterday = DateTime.now().subtract(const Duration(days: 1));
    return DateTimeRange(
      start: getStartOfDay(yesterday),
      end: getEndOfDay(yesterday),
    );
  }

  static DateTimeRange getTomorrowRange() {
    final tomorrow = DateTime.now().add(const Duration(days: 1));
    return DateTimeRange(
      start: getStartOfDay(tomorrow),
      end: getEndOfDay(tomorrow),
    );
  }

  static DateTimeRange getThisWeekRange() {
    final now = DateTime.now();
    final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
    final endOfWeek = startOfWeek.add(const Duration(days: 6));
    return DateTimeRange(
      start: getStartOfDay(startOfWeek),
      end: getEndOfDay(endOfWeek),
    );
  }

  static DateTimeRange getLastWeekRange() {
    final now = DateTime.now();
    final startOfLastWeek = now.subtract(Duration(days: now.weekday + 6));
    final endOfLastWeek = startOfLastWeek.add(const Duration(days: 6));
    return DateTimeRange(
      start: getStartOfDay(startOfLastWeek),
      end: getEndOfDay(endOfLastWeek),
    );
  }

  static DateTimeRange getThisMonthRange() {
    final now = DateTime.now();
    final startOfMonth = DateTime(now.year, now.month, 1);
    final endOfMonth = DateTime(now.year, now.month + 1, 0);
    return DateTimeRange(
      start: getStartOfDay(startOfMonth),
      end: getEndOfDay(endOfMonth),
    );
  }

  static DateTimeRange getLastMonthRange() {
    final now = DateTime.now();
    final startOfLastMonth = DateTime(now.year, now.month - 1, 1);
    final endOfLastMonth = DateTime(now.year, now.month, 0);
    return DateTimeRange(
      start: getStartOfDay(startOfLastMonth),
      end: getEndOfDay(endOfLastMonth),
    );
  }
}
