import 'package:flutter/material.dart';
import 'package:myrunway/core/models/order_model_new.dart';

/// Reusable status filter widget for order handling status
class OrderStatusFilterWidget extends StatelessWidget {
  final String title;
  final OrderHandlingStatus? selectedStatus;
  final Function(OrderHandlingStatus?) onStatusChanged;
  final bool showAllOption;

  const OrderStatusFilterWidget({
    super.key,
    required this.title,
    required this.selectedStatus,
    required this.onStatusChanged,
    this.showAllOption = true,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            if (showAllOption)
              _buildStatusChip(null, 'جميع الحالات'),
            _buildStatusChip(OrderHandlingStatus.pending, 'في الانتظار'),
            _buildStatusChip(OrderHandlingStatus.assigned, 'مُعيَّن'),
            _buildStatusChip(OrderHandlingStatus.processing, 'قيد التنفيذ'),
            _buildStatusChip(OrderHandlingStatus.completed, 'مكتمل'),
          ],
        ),
      ],
    );
  }

  Widget _buildStatusChip(OrderHandlingStatus? status, String label) {
    final isActive = selectedStatus == status;
    return ActionChip(
      label: Text(label),
      onPressed: () => onStatusChanged(status),
      backgroundColor:
          isActive ? Colors.blue.withValues(alpha: 0.1) : Colors.grey[100],
      labelStyle: TextStyle(
        fontSize: 12,
        color: isActive ? Colors.blue : Colors.black87,
        fontWeight: isActive ? FontWeight.w600 : FontWeight.normal,
      ),
      side: BorderSide(
        color: isActive ? Colors.blue : Colors.transparent,
        width: 1,
      ),
    );
  }
}

/// Utility class for status-related operations
class StatusFilterUtils {
  /// Get display text for order handling status
  static String getOrderHandlingStatusText(OrderHandlingStatus status) {
    switch (status) {
      case OrderHandlingStatus.pending:
        return 'في الانتظار';
      case OrderHandlingStatus.assigned:
        return 'مُعيَّن';
      case OrderHandlingStatus.processing:
        return 'قيد التنفيذ';
      case OrderHandlingStatus.completed:
        return 'مكتمل';
    }
  }

  /// Get color for order handling status
  static Color getOrderHandlingStatusColor(OrderHandlingStatus status) {
    switch (status) {
      case OrderHandlingStatus.pending:
        return Colors.orange;
      case OrderHandlingStatus.assigned:
        return Colors.blue;
      case OrderHandlingStatus.processing:
        return Colors.purple;
      case OrderHandlingStatus.completed:
        return Colors.green;
    }
  }

  /// Get icon for order handling status
  static IconData getOrderHandlingStatusIcon(OrderHandlingStatus status) {
    switch (status) {
      case OrderHandlingStatus.pending:
        return Icons.pending;
      case OrderHandlingStatus.assigned:
        return Icons.assignment_ind;
      case OrderHandlingStatus.processing:
        return Icons.work;
      case OrderHandlingStatus.completed:
        return Icons.check_circle;
    }
  }

  /// Get all available order handling statuses
  static List<OrderHandlingStatus> getAllOrderHandlingStatuses() {
    return OrderHandlingStatus.values;
  }

  /// Get filtered statuses based on user role or context
  static List<OrderHandlingStatus> getFilterableStatuses({
    bool includeCompleted = true,
    bool includePending = true,
  }) {
    List<OrderHandlingStatus> statuses = [];
    
    if (includePending) {
      statuses.add(OrderHandlingStatus.pending);
    }
    
    statuses.addAll([
      OrderHandlingStatus.assigned,
      OrderHandlingStatus.processing,
    ]);
    
    if (includeCompleted) {
      statuses.add(OrderHandlingStatus.completed);
    }
    
    return statuses;
  }
}
