import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:logging/logging.dart';
import 'package:myrunway/app.dart';
import 'package:myrunway/core/services/auth_service.dart';
import 'package:myrunway/core/services/api_service.dart';
import 'package:myrunway/core/services/autocomplete_service.dart';
import 'package:myrunway/core/services/company_service.dart';
import 'package:myrunway/core/services/company_channel_service.dart';
import 'package:myrunway/core/services/employee_service.dart';
import 'package:myrunway/core/services/office_service.dart';
import 'package:myrunway/core/services/order_service.dart';
import 'package:myrunway/core/services/user_service.dart';
import 'package:myrunway/core/services/user_management_service.dart';
import 'package:get/get.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  developer.log('Starting main');

  // Initialize core services
  await Get.putAsync(() => ApiService().initialize());
  await Get.putAsync(() => AuthService().init());

  // Initialize CRUD services
  await Get.putAsync(() async => CompanyService());
  await Get.putAsync(() async => CompanyChannelService());
  await Get.putAsync(() async => EmployeeService());
  await Get.putAsync(() async => OfficeService());
  await Get.putAsync(() async => OrderService());
  await Get.putAsync(() async => UserService());
  await Get.putAsync(() async => UserManagementService());
  await Get.putAsync(() async => AutocompleteService());

  // setup logger
  Logger.root.level = Level.ALL;
  Logger.root.onRecord.listen((record) {
    String levelColor =
        record.level == Level.SEVERE
            ? '\x1B[31m' // Red for severe/error
            : record.level == Level.WARNING
            ? '\x1B[33m' // Yellow for warning
            : record.level == Level.INFO
            ? '\x1B[32m' // Green for info
            : '\x1B[36m'; // Cyan for other levels
    developer.log(
      '$levelColor${record.level.name}\x1B[0m: \x1B[90m${record.time.toIso8601String().split('T')[1]}\x1B[0m: \x1B[37m${record.message}\x1B[0m',
      stackTrace: record.stackTrace,
      name: record.loggerName,
      time: record.time,
      error: record.error,
    );
  });
  runApp(const MyApp());
}
