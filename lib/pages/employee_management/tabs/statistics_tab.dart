import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:myrunway/pages/employee_management/employee_management_controller.dart';
import 'package:myrunway/pages/employee_management/employee_management_page.dart';
import 'package:myrunway/core/constants/app_colors.dart';
import 'package:myrunway/widgets/cards/info_card.dart';

class StatisticsTab extends StatelessWidget {
  const StatisticsTab({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<EmployeeManagementController>();

    return Obx(() {
      if (controller.isLoadingStats.value) {
        return const Center(child: CircularProgressIndicator());
      }

      return RefreshIndicator(
        onRefresh: controller.loadStatsData,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Employee Info Card
              EmployeeInfoCard(controller: controller),
              const SizedBox(height: 16),

              // Date Filter Controls
              DateFilterWidget(
                title: 'فترة الإحصائيات',
                currentFilter: controller.activeStatsFilter.value,
                formattedDateRange: controller.formattedStatsDateRange,
                filterOptions: controller.statsFilterOptions,
                onFilterSelected: controller.setStatsQuickFilter,
                onCustomDateRange: controller.setStatsCustomDateRange,
              ),
              const SizedBox(height: 16),

              // Statistics Data
              if (controller.hasStatsData) ...[
                _buildStatisticsContent(controller),
              ] else ...[
                _buildNoDataMessage(),
              ],
            ],
          ),
        ),
      );
    });
  }

  Widget _buildStatisticsContent(EmployeeManagementController controller) {
    final data = controller.statsData.value!;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Financial Statistics Section
        const Text(
          'الإحصائيات المالية',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),

        // First row - Money stats
        Row(
          children: [
            Expanded(
              child: StatCard(
                title: 'إجمالي المستحق',
                value: data.formattedTotalMoneyDeserved,
                icon: Icons.monetization_on,
                color: Colors.green,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: StatCard(
                title: 'المبلغ المحصل',
                value: data.formattedTotalMoneyCollected,
                icon: Icons.account_balance_wallet,
                color: Colors.blue,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),

        // Second row - Collection stats
        Row(
          children: [
            Expanded(
              child: StatCard(
                title: 'المطلوب تحصيله',
                value: data.formattedTotalMoneyToCollect,
                icon: Icons.payment,
                color: Colors.orange,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: StatCard(
                title: 'معدل التحصيل',
                value: _calculateCollectionRate(data),
                icon: Icons.trending_up,
                color: _getCollectionRateColor(data),
              ),
            ),
          ],
        ),
        const SizedBox(height: 24),

        // Order Statistics Section
        const Text(
          'إحصائيات الطلبات',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),

        Row(
          children: [
            Expanded(
              child: StatCard(
                title: 'الطلبات المكتملة',
                value: data.totalOrdersCompleted.toString(),
                icon: Icons.check_circle,
                color: Colors.green,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: StatCard(
                title: 'إجمالي الطلبات',
                value: data.totalOrdersAssigned.toString(),
                icon: Icons.assignment,
                color: AppColors.primary,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),

        Row(
          children: [
            Expanded(
              child: StatCard(
                title: 'معدل الإنجاز',
                value: data.formattedCompletionRate,
                icon: Icons.analytics,
                color:
                    data.completionRate >= 0.8 ? Colors.green : Colors.orange,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: StatCard(
                title: 'الطلبات المتبقية',
                value:
                    (data.totalOrdersAssigned - data.totalOrdersCompleted)
                        .toString(),
                icon: Icons.pending,
                color: Colors.grey,
              ),
            ),
          ],
        ),
        const SizedBox(height: 24),

        // Performance Summary Card
        _buildPerformanceSummaryCard(data),
        const SizedBox(height: 24),

        // Financial Breakdown Card
        _buildFinancialBreakdownCard(data),
      ],
    );
  }

  Widget _buildPerformanceSummaryCard(dynamic data) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.assessment, color: AppColors.primary),
                const SizedBox(width: 8),
                const Text(
                  'ملخص الأداء',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Performance indicators
            _buildPerformanceIndicator(
              'معدل إنجاز الطلبات',
              data.formattedCompletionRate,
              data.completionRate,
              Icons.check_circle,
            ),
            const SizedBox(height: 12),

            _buildPerformanceIndicator(
              'معدل التحصيل المالي',
              _calculateCollectionRate(data),
              _getCollectionRateValue(data),
              Icons.monetization_on,
            ),
            const SizedBox(height: 12),

            _buildPerformanceIndicator(
              'متوسط القيمة لكل طلب',
              _calculateAverageOrderValue(data),
              0.75, // Mock value for progress
              Icons.attach_money,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPerformanceIndicator(
    String title,
    String value,
    double progress,
    IconData icon,
  ) {
    final color =
        progress >= 0.8
            ? Colors.green
            : progress >= 0.6
            ? Colors.orange
            : Colors.red;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, size: 16, color: color),
            const SizedBox(width: 8),
            Text(
              title,
              style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
            ),
            const Spacer(),
            Text(
              value,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        LinearProgressIndicator(
          value: progress.clamp(0.0, 1.0),
          backgroundColor: Colors.grey[200],
          valueColor: AlwaysStoppedAnimation<Color>(color),
        ),
      ],
    );
  }

  Widget _buildFinancialBreakdownCard(dynamic data) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.pie_chart, color: AppColors.primary),
                const SizedBox(width: 8),
                const Text(
                  'التفصيل المالي',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),

            _buildFinancialItem(
              'إجمالي المستحق',
              data.formattedTotalMoneyDeserved,
              Colors.green,
              Icons.account_balance,
            ),
            const SizedBox(height: 8),

            _buildFinancialItem(
              'المبلغ المحصل',
              data.formattedTotalMoneyCollected,
              Colors.blue,
              Icons.check_circle,
            ),
            const SizedBox(height: 8),

            _buildFinancialItem(
              'المطلوب تحصيله',
              data.formattedTotalMoneyToCollect,
              Colors.orange,
              Icons.pending,
            ),
            const SizedBox(height: 8),

            const Divider(),
            const SizedBox(height: 8),

            _buildFinancialItem(
              'الرصيد الصافي',
              _calculateNetBalance(data),
              _getNetBalanceColor(data),
              Icons.account_balance_wallet,
              isTotal: true,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFinancialItem(
    String title,
    String value,
    Color color,
    IconData icon, {
    bool isTotal = false,
  }) {
    return Row(
      children: [
        Icon(icon, size: 16, color: color),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            title,
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.w500,
            ),
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: isTotal ? 16 : 14,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }

  // Helper methods for calculations
  String _calculateCollectionRate(dynamic data) {
    if (data.totalMoneyDeserved == 0) return '0.0%';
    final rate = (data.totalMoneyCollected / data.totalMoneyDeserved) * 100;
    return '${rate.toStringAsFixed(1)}%';
  }

  double _getCollectionRateValue(dynamic data) {
    if (data.totalMoneyDeserved == 0) return 0.0;
    return data.totalMoneyCollected / data.totalMoneyDeserved;
  }

  Color _getCollectionRateColor(dynamic data) {
    final rate = _getCollectionRateValue(data);
    if (rate >= 0.8) return Colors.green;
    if (rate >= 0.6) return Colors.orange;
    return Colors.red;
  }

  String _calculateAverageOrderValue(dynamic data) {
    if (data.totalOrdersCompleted == 0) return '0.00 جنيه';
    final average = data.totalMoneyCollected / data.totalOrdersCompleted;
    return '${average.toStringAsFixed(2)} جنيه';
  }

  String _calculateNetBalance(dynamic data) {
    final balance = data.totalMoneyDeserved - data.totalMoneyCollected;
    return '${balance.toStringAsFixed(2)} جنيه';
  }

  Color _getNetBalanceColor(dynamic data) {
    final balance = data.totalMoneyDeserved - data.totalMoneyCollected;
    return balance >= 0 ? Colors.green : Colors.red;
  }

  Widget _buildNoDataMessage() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          children: [
            Icon(Icons.bar_chart, size: 80, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'لا توجد إحصائيات',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'لا توجد إحصائيات متاحة للفترة المحددة',
              style: TextStyle(fontSize: 14, color: Colors.grey[500]),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
