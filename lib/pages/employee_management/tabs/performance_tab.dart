import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:myrunway/pages/employee_management/employee_management_controller.dart';
import 'package:myrunway/pages/employee_management/employee_management_page.dart';
import 'package:myrunway/core/constants/app_colors.dart';
import 'package:myrunway/widgets/cards/info_card.dart';

class PerformanceTab extends StatelessWidget {
  const PerformanceTab({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<EmployeeManagementController>();

    return Obx(() {
      if (controller.isLoadingPerformance.value) {
        return const Center(child: CircularProgressIndicator());
      }

      return RefreshIndicator(
        onRefresh: controller.loadPerformanceData,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Employee Info Card
              EmployeeInfoCard(controller: controller),
              const SizedBox(height: 16),

              // Date Filter Controls
              DateFilterWidget(
                title: 'فترة تقرير الأداء',
                currentFilter: controller.activePerformanceFilter.value,
                formattedDateRange: controller.formattedPerformanceDateRange,
                filterOptions: controller.performanceFilterOptions,
                onFilterSelected: controller.setPerformanceQuickFilter,
                onCustomDateRange: controller.setPerformanceCustomDateRange,
              ),
              const SizedBox(height: 16),

              // Performance Data
              if (controller.hasPerformanceData) ...[
                _buildPerformanceContent(controller),
              ] else ...[
                _buildNoDataMessage(),
              ],
            ],
          ),
        ),
      );
    });
  }

  Widget _buildPerformanceContent(EmployeeManagementController controller) {
    final data = controller.performanceData.value!;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Key Metrics Section
        const Text(
          'المؤشرات الرئيسية',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),

        // First row
        Row(
          children: [
            Expanded(
              child: StatCard(
                title: 'الطلبات المكتملة',
                value: data.totalOrdersCompleted.toString(),
                icon: Icons.check_circle,
                color: Colors.green,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: StatCard(
                title: 'إجمالي الطلبات',
                value: data.totalOrdersAssigned.toString(),
                icon: Icons.assignment,
                color: AppColors.primary,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),

        // Second row
        Row(
          children: [
            Expanded(
              child: StatCard(
                title: 'إجمالي الإيرادات',
                value: data.formattedTotalRevenue,
                icon: Icons.monetization_on,
                color: Colors.blue,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: StatCard(
                title: 'العمولة المكتسبة',
                value: data.formattedCommissionEarned,
                icon: Icons.payment,
                color: Colors.orange,
              ),
            ),
          ],
        ),
        const SizedBox(height: 24),

        // Performance Indicators Section
        const Text(
          'مؤشرات الأداء',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),

        Row(
          children: [
            Expanded(
              child: StatCard(
                title: 'معدل الإنجاز',
                value: data.formattedCompletionRate,
                icon: Icons.trending_up,
                color:
                    data.completionRate >= 0.8 ? Colors.green : Colors.orange,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: StatCard(
                title: 'التسليم في الوقت',
                value: data.formattedOnTimeDeliveryPercentage,
                icon: Icons.schedule,
                color:
                    data.onTimeDeliveryPercentage >= 0.8
                        ? Colors.green
                        : Colors.red,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),

        Row(
          children: [
            Expanded(
              child: StatCard(
                title: 'متوسط قيمة الطلب',
                value: data.formattedAverageOrderValue,
                icon: Icons.attach_money,
                color: AppColors.info,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: StatCard(
                title: 'متوسط وقت الإنجاز',
                value: data.formattedAverageCompletionTime,
                icon: Icons.timer,
                color: AppColors.warning,
              ),
            ),
          ],
        ),
        const SizedBox(height: 24),

        // Order Status Breakdown
        if (data.ordersByStatus.isNotEmpty) ...[
          _buildOrderStatusSection(data),
          const SizedBox(height: 24),
        ],

        // Daily Performance Chart
        if (data.dailyPerformance.isNotEmpty) ...[
          _buildDailyPerformanceSection(data),
        ],
      ],
    );
  }

  Widget _buildOrderStatusSection(dynamic data) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.pie_chart, color: AppColors.primary),
                const SizedBox(width: 8),
                const Text(
                  'توزيع الطلبات حسب الحالة',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...data.ordersByStatus.entries.map(
              (entry) => _buildStatusItem(entry.key, entry.value),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusItem(String status, int count) {
    Color statusColor;
    IconData statusIcon;
    String statusName;

    switch (status.toLowerCase()) {
      case 'pending':
        statusColor = Colors.orange;
        statusIcon = Icons.pending;
        statusName = 'في الانتظار';
        break;
      case 'assigned':
        statusColor = Colors.blue;
        statusIcon = Icons.assignment;
        statusName = 'مُعيَّن';
        break;
      case 'processing':
        statusColor = Colors.purple;
        statusIcon = Icons.work;
        statusName = 'قيد التنفيذ';
        break;
      case 'completed':
        statusColor = Colors.green;
        statusIcon = Icons.check_circle;
        statusName = 'مكتمل';
        break;
      default:
        statusColor = Colors.grey;
        statusIcon = Icons.help;
        statusName = status;
        break;
    }

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Container(
            width: 12,
            height: 12,
            decoration: BoxDecoration(
              color: statusColor,
              borderRadius: BorderRadius.circular(6),
            ),
          ),
          const SizedBox(width: 12),
          Icon(statusIcon, size: 16, color: statusColor),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              statusName,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Text(
            count.toString(),
            style: TextStyle(fontWeight: FontWeight.bold, color: statusColor),
          ),
        ],
      ),
    );
  }

  Widget _buildDailyPerformanceSection(dynamic data) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.show_chart, color: AppColors.primary),
                const SizedBox(width: 8),
                const Text(
                  'الأداء اليومي',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Show last 7 days of performance
            ...data.dailyPerformance
                .take(7)
                .map((daily) => _buildDailyPerformanceItem(daily)),
          ],
        ),
      ),
    );
  }

  Widget _buildDailyPerformanceItem(dynamic daily) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6),
      child: Row(
        children: [
          SizedBox(
            width: 80,
            child: Text(
              daily.formattedDate,
              style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            flex: 2,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.assignment, size: 14, color: Colors.grey[600]),
                    const SizedBox(width: 4),
                    Text(
                      '${daily.completedOrders}/${daily.totalOrders}',
                      style: const TextStyle(fontSize: 12),
                    ),
                  ],
                ),
                const SizedBox(height: 2),
                LinearProgressIndicator(
                  value: daily.completionRate,
                  backgroundColor: Colors.grey[200],
                  valueColor: AlwaysStoppedAnimation<Color>(
                    daily.completionRate >= 0.8 ? Colors.green : Colors.orange,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              daily.formattedRevenue,
              style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w600),
              textAlign: TextAlign.end,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNoDataMessage() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          children: [
            Icon(Icons.analytics_outlined, size: 80, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'لا توجد بيانات أداء',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'لا توجد بيانات أداء متاحة للفترة المحددة',
              style: TextStyle(fontSize: 14, color: Colors.grey[500]),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
