import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:myrunway/core/models/employee_performance_models.dart';
import 'package:myrunway/core/models/employee_balance_models.dart';
import 'package:myrunway/core/models/employee_stats_model.dart';
import 'package:myrunway/core/models/user_model.dart';
import 'package:myrunway/core/services/auth_service.dart';
import 'package:myrunway/core/services/employee_service.dart';
import 'package:myrunway/core/constants/user_roles.dart';
import 'package:myrunway/models/filter_option.dart';

class EmployeeManagementController extends GetxController
    with GetSingleTickerProviderStateMixin {
  final EmployeeService _employeeService = Get.find<EmployeeService>();
  final AuthService _authService = Get.find<AuthService>();

  // Tab controller
  late TabController tabController;

  // Employee data
  final Rx<UserModel?> employee = Rx<UserModel?>(null);
  final RxInt employeeId = 0.obs;

  // Loading states
  final RxBool isLoadingPerformance = false.obs;
  final RxBool isLoadingStats = false.obs;
  final RxBool isLoadingBalance = false.obs;
  final RxBool isSettlingBalance = false.obs;
  final RxString error = ''.obs;

  // Data
  final Rx<EmployeePerformanceModel?> performanceData =
      Rx<EmployeePerformanceModel?>(null);
  final Rx<EmployeeStatsModel?> statsData = Rx<EmployeeStatsModel?>(null);
  final RxList<EmployeeBalanceTransactionModel> balanceTransactions =
      <EmployeeBalanceTransactionModel>[].obs;
  final Rx<EmployeeBalanceSummary?> balanceSummary =
      Rx<EmployeeBalanceSummary?>(null);

  // Performance filter states
  final Rx<DateTime?> performanceDateFrom = Rx<DateTime?>(null);
  final Rx<DateTime?> performanceDateTo = Rx<DateTime?>(null);
  final RxString activePerformanceFilter = 'last_30_days'.obs;

  // Statistics filter states
  final Rx<DateTime?> statsDateFrom = Rx<DateTime?>(null);
  final Rx<DateTime?> statsDateTo = Rx<DateTime?>(null);
  final RxString activeStatsFilter = 'last_settlement'.obs;

  // Getters
  bool get isMasterOrManager {
    final role = _authService.currentUser?.role;
    return role == UserRole.master || role == UserRole.manager;
  }

  bool get hasError => error.value.isNotEmpty;
  bool get hasPerformanceData => performanceData.value != null;
  bool get hasStatsData => statsData.value != null;
  bool get hasBalanceData => balanceTransactions.isNotEmpty;

  @override
  void onInit() {
    super.onInit();

    // Initialize tab controller
    tabController = TabController(length: 3, vsync: this);

    // Check access permissions
    if (!isMasterOrManager) {
      Get.snackbar(
        'خطأ في الصلاحية',
        'ليس لديك صلاحية للوصول إلى هذه الصفحة',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      Get.back();
      return;
    }

    // Get employee ID from arguments
    final args = Get.arguments;
    if (args != null && args is Map<String, dynamic>) {
      employeeId.value = args['employeeId'] ?? 0;
      employee.value = args['employee'];
    }

    if (employeeId.value == 0) {
      error.value = 'معرف الموظف غير صحيح';
      return;
    }

    // Set default date ranges
    setPerformanceQuickFilter('last_30_days', false);
    setStatsQuickFilter('last_settlement', false);

    // Load initial data
    loadAllData();
  }

  @override
  void onClose() {
    tabController.dispose();
    super.onClose();
  }

  // Load all data
  Future<void> loadAllData() async {
    await Future.wait([
      loadPerformanceData(),
      loadStatsData(),
      loadBalanceData(),
    ]);
  }

  // Refresh all data
  Future<void> refreshAllData() async {
    await loadAllData();
    Get.snackbar(
      'تم التحديث',
      'تم تحديث جميع البيانات بنجاح',
      backgroundColor: Colors.green,
      colorText: Colors.white,
    );
  }

  // Performance data methods
  Future<void> loadPerformanceData() async {
    isLoadingPerformance.value = true;
    error.value = '';

    final response = await _employeeService.getEmployeePerformance(
      employeeId.value,
      startDate: performanceDateFrom.value,
      endDate: performanceDateTo.value,
    );

    if (response.success && response.data != null) {
      performanceData.value = response.data!;
    } else {
      error.value = response.message ?? 'فشل في جلب بيانات الأداء';
    }

    isLoadingPerformance.value = false;
  }

  void setPerformanceQuickFilter(String filterCode, [bool loadData = true]) {
    activePerformanceFilter.value = filterCode;
    final now = DateTime.now();

    switch (filterCode) {
      case 'today':
        performanceDateFrom.value = DateTime(now.year, now.month, now.day);
        performanceDateTo.value = now;
        break;
      case 'yesterday':
        final yesterday = now.subtract(const Duration(days: 1));
        performanceDateFrom.value = DateTime(
          yesterday.year,
          yesterday.month,
          yesterday.day,
        );
        performanceDateTo.value = DateTime(
          yesterday.year,
          yesterday.month,
          yesterday.day,
          23,
          59,
          59,
        );
        break;
      case 'this_week':
        final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
        performanceDateFrom.value = DateTime(
          startOfWeek.year,
          startOfWeek.month,
          startOfWeek.day,
        );
        performanceDateTo.value = now;
        break;
      case 'last_week':
        final lastWeekEnd = now.subtract(Duration(days: now.weekday));
        final lastWeekStart = lastWeekEnd.subtract(const Duration(days: 6));
        performanceDateFrom.value = DateTime(
          lastWeekStart.year,
          lastWeekStart.month,
          lastWeekStart.day,
        );
        performanceDateTo.value = DateTime(
          lastWeekEnd.year,
          lastWeekEnd.month,
          lastWeekEnd.day,
          23,
          59,
          59,
        );
        break;
      case 'this_month':
        performanceDateFrom.value = DateTime(now.year, now.month, 1);
        performanceDateTo.value = now;
        break;
      case 'last_month':
        final lastMonth = DateTime(now.year, now.month - 1, 1);
        final lastMonthEnd = DateTime(now.year, now.month, 0);
        performanceDateFrom.value = lastMonth;
        performanceDateTo.value = DateTime(
          lastMonthEnd.year,
          lastMonthEnd.month,
          lastMonthEnd.day,
          23,
          59,
          59,
        );
        break;
      case 'last_7_days':
        performanceDateFrom.value = now.subtract(const Duration(days: 7));
        performanceDateTo.value = now;
        break;
      case 'last_30_days':
        performanceDateFrom.value = now.subtract(const Duration(days: 30));
        performanceDateTo.value = now;
        break;
      default:
        // Default to last 30 days for performance
        performanceDateFrom.value = now.subtract(const Duration(days: 30));
        performanceDateTo.value = now;
        break;
    }

    if (loadData) {
      loadPerformanceData();
    }
  }

  Future<void> setPerformanceCustomDateRange() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: Get.context!,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange:
          performanceDateFrom.value != null && performanceDateTo.value != null
              ? DateTimeRange(
                start: performanceDateFrom.value!,
                end: performanceDateTo.value!,
              )
              : null,
      locale: const Locale('ar', 'SA'),
    );

    if (picked != null) {
      performanceDateFrom.value = picked.start;
      performanceDateTo.value = picked.end;
      activePerformanceFilter.value = 'custom_range';
      loadPerformanceData();
    }
  }

  String get formattedPerformanceDateRange {
    if (performanceDateFrom.value == null || performanceDateTo.value == null) {
      return 'غير محدد';
    }

    final from = performanceDateFrom.value!;
    final to = performanceDateTo.value!;

    if (activePerformanceFilter.value == 'custom_range') {
      return '${from.day}/${from.month}/${from.year} - ${to.day}/${to.month}/${to.year}';
    }

    // Convert code to Arabic label for display
    final opt = performanceFilterOptions.firstWhere(
      (o) => o.code == activePerformanceFilter.value,
      orElse: () => const FilterOption('', ''),
    );
    return opt.label.isNotEmpty ? opt.label : 'غير محدد';
  }

  // Statistics data methods
  Future<void> loadStatsData() async {
    isLoadingStats.value = true;
    error.value = '';

    final response = await _employeeService.getEmployeeStats(
      employeeId.value,
      dateFrom: statsDateFrom.value,
      dateTo: statsDateTo.value,
    );

    if (response.success && response.data != null) {
      statsData.value = response.data!;
    } else {
      error.value = response.message ?? 'فشل في جلب الإحصائيات';
    }

    isLoadingStats.value = false;
  }

  void setStatsQuickFilter(String filterCode, [bool loadData = true]) {
    activeStatsFilter.value = filterCode;
    final now = DateTime.now();

    switch (filterCode) {
      case 'today':
        statsDateFrom.value = DateTime(now.year, now.month, now.day);
        statsDateTo.value = now;
        break;
      case 'yesterday':
        final yesterday = now.subtract(const Duration(days: 1));
        statsDateFrom.value = DateTime(
          yesterday.year,
          yesterday.month,
          yesterday.day,
        );
        statsDateTo.value = DateTime(
          yesterday.year,
          yesterday.month,
          yesterday.day,
          23,
          59,
          59,
        );
        break;
      case 'this_week':
        final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
        statsDateFrom.value = DateTime(
          startOfWeek.year,
          startOfWeek.month,
          startOfWeek.day,
        );
        statsDateTo.value = now;
        break;
      case 'last_week':
        final lastWeekEnd = now.subtract(Duration(days: now.weekday));
        final lastWeekStart = lastWeekEnd.subtract(const Duration(days: 6));
        statsDateFrom.value = DateTime(
          lastWeekStart.year,
          lastWeekStart.month,
          lastWeekStart.day,
        );
        statsDateTo.value = DateTime(
          lastWeekEnd.year,
          lastWeekEnd.month,
          lastWeekEnd.day,
          23,
          59,
          59,
        );
        break;
      case 'this_month':
        statsDateFrom.value = DateTime(now.year, now.month, 1);
        statsDateTo.value = now;
        break;
      case 'last_month':
        final lastMonth = DateTime(now.year, now.month - 1, 1);
        final lastMonthEnd = DateTime(now.year, now.month, 0);
        statsDateFrom.value = lastMonth;
        statsDateTo.value = DateTime(
          lastMonthEnd.year,
          lastMonthEnd.month,
          lastMonthEnd.day,
          23,
          59,
          59,
        );
        break;
      case 'last_7_days':
        statsDateFrom.value = now.subtract(const Duration(days: 7));
        statsDateTo.value = now;
        break;
      case 'last_30_days':
        statsDateFrom.value = now.subtract(const Duration(days: 30));
        statsDateTo.value = now;
        break;
      case 'last_settlement':
        // Set both dates to null to get all statistics data since last settlement
        statsDateFrom.value = null;
        statsDateTo.value = null;
        break;
      default:
        // Default to "last_settlement" instead of 30 days for statistics
        statsDateFrom.value = null;
        statsDateTo.value = null;
        break;
    }

    if (loadData) {
      loadStatsData();
    }
  }

  Future<void> setStatsCustomDateRange() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: Get.context!,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange:
          statsDateFrom.value != null && statsDateTo.value != null
              ? DateTimeRange(
                start: statsDateFrom.value!,
                end: statsDateTo.value!,
              )
              : null,
      locale: const Locale('ar', 'SA'),
    );

    if (picked != null) {
      statsDateFrom.value = picked.start;
      statsDateTo.value = picked.end;
      activeStatsFilter.value = 'custom_range';
      loadStatsData();
    }
  }

  String get formattedStatsDateRange {
    // Handle "last_settlement" filter specifically
    if (activeStatsFilter.value == 'last_settlement') {
      return 'آخر تسوية';
    }

    if (statsDateFrom.value == null || statsDateTo.value == null) {
      return 'غير محدد';
    }

    final from = statsDateFrom.value!;
    final to = statsDateTo.value!;

    if (activeStatsFilter.value == 'custom_range') {
      return '${from.day}/${from.month}/${from.year} - ${to.day}/${to.month}/${to.year}';
    }

    final opt = statsFilterOptions.firstWhere(
      (o) => o.code == activeStatsFilter.value,
      orElse: () => const FilterOption('', ''),
    );
    return opt.label.isNotEmpty ? opt.label : 'غير محدد';
  }

  // Balance data methods
  Future<void> loadBalanceData() async {
    isLoadingBalance.value = true;
    error.value = '';

    final transactionsResponse = await _employeeService
        .getEmployeeBalanceTransactions(employeeId.value);

    if (transactionsResponse.success && transactionsResponse.data != null) {
      balanceTransactions.value = transactionsResponse.data!;

      // Calculate balance summary
      final summaryResponse = await _employeeService.getEmployeeBalanceSummary(
        employeeId.value,
      );
      if (summaryResponse.success && summaryResponse.data != null) {
        balanceSummary.value = summaryResponse.data!;
      }
    } else {
      error.value = transactionsResponse.message ?? 'فشل في جلب بيانات الرصيد';
    }

    isLoadingBalance.value = false;
  }

  // Settle employee balance
  Future<void> settleEmployeeBalance({String? notes}) async {
    // Show confirmation dialog
    final confirmed = await Get.dialog<bool>(
      AlertDialog(
        title: const Text('تأكيد التسوية'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'هل أنت متأكد من تسوية رصيد ${employee.value?.fullName ?? 'الموظف'}؟',
            ),
            const SizedBox(height: 8),
            if (balanceSummary.value != null)
              Text(
                'الرصيد الحالي: ${balanceSummary.value!.formattedCurrentBalance}',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
            const SizedBox(height: 8),
            const Text(
              'سيتم إنشاء معاملة تسوية وإعادة تعيين الرصيد إلى صفر.',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Get.back(result: true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('تأكيد التسوية'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    isSettlingBalance.value = true;

    final response = await _employeeService.settleEmployeeBalance(
      employeeId.value,
      notes: notes,
    );

    if (response.success) {
      Get.snackbar(
        'تمت التسوية',
        'تم تسوية رصيد الموظف بنجاح',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );

      // Reload balance data
      await loadBalanceData();
    } else {
      Get.snackbar(
        'خطأ',
        response.message ?? 'فشل في تسوية الرصيد',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }

    isSettlingBalance.value = false;
  }

  // Quick filter options for performance tab
  List<FilterOption> get performanceFilterOptions => const [
    FilterOption('today', 'اليوم'),
    FilterOption('yesterday', 'أمس'),
    FilterOption('this_week', 'هذا الأسبوع'),
    FilterOption('last_week', 'الأسبوع الماضي'),
    FilterOption('this_month', 'هذا الشهر'),
    FilterOption('last_month', 'الشهر الماضي'),
    FilterOption('last_7_days', 'آخر 7 أيام'),
    FilterOption('last_30_days', 'آخر 30 يوم'),
  ];

  // Quick filter options for statistics tab
  List<FilterOption> get statsFilterOptions => const [
    FilterOption('last_settlement', 'آخر تسوية'),
    FilterOption('today', 'اليوم'),
    FilterOption('yesterday', 'أمس'),
    FilterOption('this_week', 'هذا الأسبوع'),
    FilterOption('last_week', 'الأسبوع الماضي'),
    FilterOption('this_month', 'هذا الشهر'),
    FilterOption('last_month', 'الشهر الماضي'),
    FilterOption('last_7_days', 'آخر 7 أيام'),
    FilterOption('last_30_days', 'آخر 30 يوم'),
  ];
}
