import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:myrunway/pages/employee_management/employee_management_controller.dart';
import 'package:myrunway/pages/employee_management/tabs/performance_tab.dart';
import 'package:myrunway/pages/employee_management/tabs/statistics_tab.dart';
import 'package:myrunway/pages/employee_management/tabs/balance_tab.dart';
import 'package:myrunway/core/constants/app_colors.dart';
import 'package:myrunway/models/filter_option.dart';

class EmployeeManagementPage extends StatelessWidget {
  const EmployeeManagementPage({super.key});

  @override
  Widget build(BuildContext context) {
    final EmployeeManagementController controller = Get.put(
      EmployeeManagementController(),
    );

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: Obx(
          () => Text(
            controller.employee.value?.fullName ?? 'إدارة الموظف',
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          Obx(
            () => IconButton(
              icon:
                  controller.isLoadingPerformance.value ||
                          controller.isLoadingStats.value ||
                          controller.isLoadingBalance.value
                      ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                      : const Icon(Icons.refresh),
              onPressed: controller.refreshAllData,
            ),
          ),
        ],
        bottom: TabBar(
          controller: controller.tabController,
          labelColor: AppColors.primary,
          unselectedLabelColor: Colors.grey,
          indicatorColor: AppColors.primary,
          indicatorWeight: 3,
          tabs: const [
            Tab(icon: Icon(Icons.analytics), text: 'الأداء'),
            Tab(icon: Icon(Icons.bar_chart), text: 'الإحصائيات'),
            Tab(icon: Icon(Icons.account_balance_wallet), text: 'الرصيد'),
          ],
        ),
      ),
      body: Obx(() {
        if (controller.hasError && controller.employeeId.value == 0) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error_outline, size: 64, color: Colors.red[300]),
                const SizedBox(height: 16),
                Text(
                  controller.error.value,
                  style: const TextStyle(fontSize: 16),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () => Get.back(),
                  child: const Text('العودة'),
                ),
              ],
            ),
          );
        }

        return TabBarView(
          controller: controller.tabController,
          children: const [PerformanceTab(), StatisticsTab(), BalanceTab()],
        );
      }),
    );
  }
}

// Employee Info Card Widget
class EmployeeInfoCard extends StatelessWidget {
  final EmployeeManagementController controller;

  const EmployeeInfoCard({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final employee = controller.employee.value;
      if (employee == null) return const SizedBox.shrink();

      return Card(
        elevation: 2,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: AppColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(30),
                ),
                child: Icon(Icons.person, color: AppColors.primary, size: 30),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      employee.fullName,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      employee.email ?? 'لا يوجد بريد إلكتروني',
                      style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                    ),
                    const SizedBox(height: 4),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: _getRoleColor(
                          employee.role,
                        ).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        _getRoleText(employee.role),
                        style: TextStyle(
                          fontSize: 12,
                          color: _getRoleColor(employee.role),
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Column(
                children: [
                  Text(
                    'معرف الموظف',
                    style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                  ),
                  Text(
                    '#${employee.id}',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      );
    });
  }

  Color _getRoleColor(dynamic role) {
    switch (role.toString().toLowerCase()) {
      case 'master':
        return Colors.purple;
      case 'manager':
        return Colors.blue;
      case 'employee':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  String _getRoleText(dynamic role) {
    switch (role.toString().toLowerCase()) {
      case 'master':
        return 'مدير عام';
      case 'manager':
        return 'مدير';
      case 'employee':
        return 'موظف';
      default:
        return 'غير محدد';
    }
  }
}

// Date Filter Widget
class DateFilterWidget extends StatelessWidget {
  final String title;
  final String currentFilter;
  final String formattedDateRange;
  final List<FilterOption> filterOptions;
  final Function(String) onFilterSelected;
  final VoidCallback onCustomDateRange;

  const DateFilterWidget({
    super.key,
    required this.title,
    required this.currentFilter,
    required this.formattedDateRange,
    required this.filterOptions,
    required this.onFilterSelected,
    required this.onCustomDateRange,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.date_range, color: AppColors.primary),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Text(
                  formattedDateRange,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Quick filter buttons
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children:
                  filterOptions.map((filter) {
                    final isActive = currentFilter == filter.code;
                    return InkWell(
                      onTap: () => onFilterSelected(filter.code),
                      borderRadius: BorderRadius.circular(20),
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 6,
                        ),
                        decoration: BoxDecoration(
                          color:
                              isActive ? AppColors.primary : Colors.grey[100],
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(
                            color:
                                isActive
                                    ? AppColors.primary
                                    : Colors.grey[300]!,
                          ),
                        ),
                        child: Text(
                          filter.label,
                          style: TextStyle(
                            color: isActive ? Colors.white : Colors.grey[700],
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    );
                  }).toList(),
            ),
            const SizedBox(height: 12),

            // Custom date range button
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: onCustomDateRange,
                icon: const Icon(Icons.calendar_today, size: 16),
                label: const Text('اختيار نطاق مخصص'),
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
