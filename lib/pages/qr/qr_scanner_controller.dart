import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'package:myrunway/core/services/auth_service.dart';
import 'package:myrunway/core/constants/user_roles.dart';
import 'package:myrunway/pages/employee/employee_entry_page.dart';
import 'package:myrunway/pages/manager/home_page.dart';
import 'package:myrunway/pages/master/home_page.dart';
import 'package:logging/logging.dart';

final _logger = Logger('QRScannerController');

class QRScannerController extends GetxController {
  final AuthService _authService = Get.find<AuthService>();
  
  // Scanner controller
  late MobileScannerController scannerController;
  
  // Observable variables
  final RxBool isScanning = true.obs;
  final RxBool isProcessing = false.obs;
  final RxString scannedData = ''.obs;

  @override
  void onInit() {
    super.onInit();
    _logger.info('QRScannerController initialized');
    
    // Initialize scanner
    scannerController = MobileScannerController(
      detectionSpeed: DetectionSpeed.noDuplicates,
      facing: CameraFacing.back,
      torchEnabled: false,
    );
  }

  @override
  void onClose() {
    _logger.info('QRScannerController disposing');
    scannerController.dispose();
    super.onClose();
  }

  /// Handle QR code detection
  void onDetect(BarcodeCapture capture) {
    if (isProcessing.value) {
      _logger.info('QR detection ignored - already processing');
      return;
    }

    final List<Barcode> barcodes = capture.barcodes;
    if (barcodes.isEmpty) {
      _logger.warning('No barcodes detected');
      return;
    }

    final barcode = barcodes.first;
    final qrToken = barcode.rawValue;
    
    if (qrToken == null || qrToken.isEmpty) {
      _logger.warning('Empty QR token detected');
      Get.snackbar(
        'خطأ في رمز QR',
        'رمز QR غير صالح',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    _logger.info('QR token detected: ${qrToken.substring(0, 10)}...');
    scannedData.value = qrToken;
    processQRLogin(qrToken);
  }

  /// Process QR login with the scanned token
  Future<void> processQRLogin(String qrToken) async {
    _logger.info('Processing QR login');
    isProcessing.value = true;
    isScanning.value = false;

    try {
      // Stop scanner while processing
      await scannerController.stop();

      final success = await _authService.loginWithQR(qrToken);
      
      if (success) {
        _logger.info('QR login successful, navigating to home page');
        _navigateToHomePage();
      } else {
        _logger.warning('QR login failed');
        // Error message is already shown by AuthService
        _resetScanner();
      }
    } catch (e) {
      _logger.severe('Exception during QR login: $e');
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تسجيل الدخول',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      _resetScanner();
    } finally {
      isProcessing.value = false;
    }
  }

  /// Navigate to appropriate home page based on user role
  void _navigateToHomePage() {
    _logger.info('Starting navigation to home page');

    final userRole = _authService.currentUserRole;
    _logger.info('Current user role: $userRole');

    if (userRole == null) {
      _logger.severe('Navigation failed: User role is null');
      Get.snackbar(
        'خطأ',
        'لم يتم تحديد دور المستخدم',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    Widget homePage;

    switch (userRole) {
      case UserRole.employee:
        _logger.info('Navigating to Employee home page');
        homePage = const EmployeeEntryPage();
        break;
      case UserRole.manager:
        _logger.info('Navigating to Manager home page');
        homePage = const ManagerHomePage();
        break;
      case UserRole.master:
        _logger.info('Navigating to Master home page');
        homePage = const MasterHomePage();
        break;
    }

    _logger.info('Navigation completed');
    Get.offAll(() => homePage);
  }

  /// Reset scanner to continue scanning
  void _resetScanner() async {
    _logger.info('Resetting scanner');
    try {
      await scannerController.start();
      isScanning.value = true;
      scannedData.value = '';
    } catch (e) {
      _logger.severe('Failed to reset scanner: $e');
    }
  }

  /// Toggle flashlight
  void toggleFlash() async {
    _logger.info('Toggling flashlight');
    try {
      await scannerController.toggleTorch();
    } catch (e) {
      _logger.warning('Failed to toggle flashlight: $e');
    }
  }

  /// Switch camera
  void switchCamera() async {
    _logger.info('Switching camera');
    try {
      await scannerController.switchCamera();
    } catch (e) {
      _logger.warning('Failed to switch camera: $e');
    }
  }

  /// Go back to login page
  void goBack() {
    _logger.info('Going back to login page');
    Get.back();
  }
}
