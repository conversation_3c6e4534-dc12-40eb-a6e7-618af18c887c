import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:myrunway/core/services/auth_service.dart';
import 'package:myrunway/core/services/user_service.dart';
import 'package:myrunway/core/models/user_model.dart';
import 'package:myrunway/core/models/qr_code_models.dart';
import 'package:myrunway/core/constants/user_roles.dart';
import 'package:logging/logging.dart';

final _logger = Logger('QRGenerationController');

class QRGenerationController extends GetxController {
  final AuthService _authService = Get.find<AuthService>();
  final UserService _userService = Get.find<UserService>();

  // Observable variables
  final RxList<UserModel> employees = <UserModel>[].obs;
  final Rx<UserModel?> selectedEmployee = Rx<UserModel?>(null);
  final RxBool isLoadingEmployees = false.obs;
  final RxBool isGeneratingQR = false.obs;
  final Rx<QRCodeGenerateResponse?> qrCodeResponse = Rx<QRCodeGenerateResponse?>(null);

  @override
  void onInit() {
    super.onInit();
    _logger.info('QRGenerationController initialized');
    
    // Check if user is Master
    if (_authService.currentUserRole != UserRole.master) {
      _logger.warning('Access denied - user is not Master');
      Get.snackbar(
        'خطأ في الصلاحية',
        'فقط المدير العام يمكنه الوصول إلى هذه الصفحة',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      Get.back();
      return;
    }
    
    loadEmployees();
  }

  /// Load all employees from the office
  Future<void> loadEmployees() async {
    _logger.info('Loading employees for QR generation');
    isLoadingEmployees.value = true;

    try {
      final response = await _userService.getEmployees();
      
      if (response.success && response.data != null) {
        employees.value = response.data!;
        _logger.info('Loaded ${employees.length} employees');
      } else {
        _logger.warning('Failed to load employees: ${response.message}');
        Get.snackbar(
          'خطأ',
          response.message ?? 'فشل في جلب قائمة الموظفين',
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      _logger.severe('Exception while loading employees: $e');
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء جلب قائمة الموظفين',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoadingEmployees.value = false;
    }
  }

  /// Select an employee for QR generation
  void selectEmployee(UserModel? employee) {
    _logger.info('Employee selected: ${employee?.fullName ?? 'None'}');
    selectedEmployee.value = employee;
    // Clear previous QR code when selecting a different employee
    qrCodeResponse.value = null;
  }

  /// Generate QR code for the selected employee
  Future<void> generateQRCode() async {
    if (selectedEmployee.value == null) {
      _logger.warning('QR generation attempted without selecting employee');
      Get.snackbar(
        'خطأ',
        'يرجى اختيار موظف أولاً',
        backgroundColor: Colors.orange,
        colorText: Colors.white,
      );
      return;
    }

    final employeeId = selectedEmployee.value!.id;
    if (employeeId == null) {
      _logger.warning('Selected employee has no ID');
      Get.snackbar(
        'خطأ',
        'معرف الموظف غير صالح',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    _logger.info('Generating QR code for employee ID: $employeeId');
    isGeneratingQR.value = true;

    try {
      final response = await _authService.generateQRCode(employeeId);
      
      if (response != null) {
        qrCodeResponse.value = response;
        _logger.info('QR code generated successfully');
        Get.snackbar(
          'نجح',
          'تم إنشاء رمز QR بنجاح',
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
      } else {
        _logger.warning('QR code generation returned null');
        // Error message is already shown by AuthService
      }
    } catch (e) {
      _logger.severe('Exception during QR generation: $e');
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء إنشاء رمز QR',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isGeneratingQR.value = false;
    }
  }

  /// Clear the generated QR code
  void clearQRCode() {
    _logger.info('Clearing QR code');
    qrCodeResponse.value = null;
  }

  /// Refresh employees list
  Future<void> refreshEmployees() async {
    _logger.info('Refreshing employees list');
    await loadEmployees();
  }
}
