import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:myrunway/core/services/auth_service.dart';
import 'package:myrunway/core/constants/user_roles.dart';
import 'package:myrunway/pages/employee/employee_entry_page.dart';
import 'package:myrunway/pages/manager/home_page.dart';
import 'package:myrunway/pages/master/home_page.dart';

import 'package:logging/logging.dart';

final _logger = Logger('LoginController');

class LoginController extends GetxController {
  final AuthService _authService = Get.find<AuthService>();

  // Reactive variables
  final RxBool isLoading = false.obs;
  final RxString username = ''.obs;
  final RxString password = ''.obs;
  final RxBool obscurePassword = true.obs;
  final RxInt selectedOfficeId = 1.obs; // Default office ID

  @override
  void onInit() {
    super.onInit();
    _logger.info('LoginController initialized');
    _logger.fine(
      'Initial state - username: empty, password: empty, selectedOfficeId: ${selectedOfficeId.value}',
    );
  }

  void togglePasswordVisibility() {
    _logger.fine(
      'Toggling password visibility from ${obscurePassword.value} to ${!obscurePassword.value}',
    );
    obscurePassword.value = !obscurePassword.value;
    _logger.fine('Password visibility toggled to ${obscurePassword.value}');
  }

  Future<void> login() async {
    _logger.info('Login attempt started');
    _logger.fine(
      'Login parameters - username: ${username.value.trim()}, officeId: ${selectedOfficeId.value}',
    );

    if (!_validateInputs()) {
      _logger.warning('Login validation failed');
      return;
    }

    _logger.info('Input validation passed, proceeding with authentication');
    isLoading.value = true;
    _logger.fine('Loading state set to true');

    _logger.info(
      'Calling AuthService.login with username: ${username.value.trim()}, officeId: ${selectedOfficeId.value}',
    );

    final success = await _authService.login(
      username.value.trim(),
      password.value,
      selectedOfficeId.value,
    );

    _logger.info('AuthService.login completed with result: $success');

    if (success) {
      _logger.info('Login successful, navigating to home page');
      _navigateToHomePage();
    } else {
      _logger.warning('Login failed - AuthService returned false');
      Get.snackbar(
        'خطأ في تسجيل الدخول',
        'اسم المستخدم أو كلمة المرور غير صحيحة',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }

    _logger.fine('Setting loading state to false');
    isLoading.value = false;
    _logger.info('Login attempt completed');
  }

  bool _validateInputs() {
    _logger.fine('Starting input validation');

    // Username validation
    _logger.fine('Validating username: "${username.value.trim()}"');
    if (username.value.trim().isEmpty) {
      _logger.warning('Validation failed: Username is empty');
      Get.snackbar(
        'خطأ في البيانات',
        'يرجى إدخال اسم المستخدم',
        backgroundColor: Colors.orange,
        colorText: Colors.white,
      );
      return false;
    }

    if (username.value.trim().length < 3) {
      _logger.warning(
        'Validation failed: Username too short (${username.value.trim().length} characters)',
      );
      Get.snackbar(
        'خطأ في البيانات',
        'اسم المستخدم يجب أن يكون 3 أحرف على الأقل',
        backgroundColor: Colors.orange,
        colorText: Colors.white,
      );
      return false;
    }
    _logger.fine('Username validation passed');

    // Password validation
    _logger.fine('Validating password (length: ${password.value.length})');
    if (password.value.isEmpty) {
      _logger.warning('Validation failed: Password is empty');
      Get.snackbar(
        'خطأ في البيانات',
        'يرجى إدخال كلمة المرور',
        backgroundColor: Colors.orange,
        colorText: Colors.white,
      );
      return false;
    }

    if (password.value.length < 6) {
      _logger.warning(
        'Validation failed: Password too short (${password.value.length} characters)',
      );
      Get.snackbar(
        'خطأ في البيانات',
        'كلمة المرور يجب أن تكون 6 أحرف على الأقل',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    }
    _logger.fine('Password validation passed');

    // Office ID validation
    _logger.fine('Validating office ID: ${selectedOfficeId.value}');
    if (selectedOfficeId.value <= 0) {
      _logger.warning(
        'Validation failed: Invalid office ID (${selectedOfficeId.value})',
      );
      Get.snackbar(
        'خطأ في البيانات',
        'يرجى اختيار المكتب',
        backgroundColor: Colors.orange,
        colorText: Colors.white,
      );
      return false;
    }
    _logger.fine('Office ID validation passed');

    _logger.info('All input validations passed');
    return true;
  }

  void _navigateToHomePage() {
    _logger.info('Starting navigation to home page');

    final userRole = _authService.currentUserRole;
    _logger.info('Current user role: $userRole');

    if (userRole == null) {
      _logger.severe('Navigation failed: User role is null');
      Get.snackbar(
        'خطأ',
        'لم يتم تحديد دور المستخدم',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    Widget homePage;

    switch (userRole) {
      case UserRole.employee:
        _logger.info('Navigating to Employee home page');
        homePage = const EmployeeEntryPage();
        break;
      case UserRole.manager:
        _logger.info('Navigating to Manager home page');
        homePage = const ManagerHomePage();
        break;
      case UserRole.master:
        _logger.info('Navigating to Master home page');
        homePage = const MasterHomePage();
        break;
    }

    _logger.info('Replacing navigation stack with home page');
    // Use Get.offAll to replace the entire navigation stack
    Get.offAll(() => homePage);
    _logger.info('Navigation completed successfully');
  }

  void forgotPassword() {
    _logger.info('Forgot password dialog requested');

    // Navigate to forgot password page or show dialog
    Get.dialog(
      AlertDialog(
        title: const Text('نسيت كلمة المرور؟'),
        content: const Text('يرجى التواصل مع المدير لإعادة تعيين كلمة المرور.'),
        actions: [
          TextButton(
            onPressed: () {
              _logger.fine('Forgot password dialog dismissed');
              Get.back();
            },
            child: const Text('موافق'),
          ),
        ],
      ),
    );

    _logger.info('Forgot password dialog displayed');
  }

  @override
  void onClose() {
    _logger.info('LoginController disposing - clearing sensitive data');

    // Clear sensitive data when controller is disposed
    username.value = '';
    password.value = '';

    _logger.fine('Sensitive data cleared');
    super.onClose();
    _logger.info('LoginController disposed');
  }
}
