import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

// Style 1: Modern Floating Navbar
class FloatingNavBar extends StatelessWidget {
  final int currentIndex;
  final Function(int) onTap;

  const FloatingNavBar({
    super.key,
    required this.currentIndex,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(20),
      height: 70,
      decoration: BoxDecoration(
        color: Colors.black87,
        borderRadius: BorderRadius.circular(25),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.3),
            blurRadius: 30,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildNavItem(0, Icons.shopping_bag_outlined, Icons.shopping_bag),
          _buildNavItem(1, Icons.inventory_2_outlined, Icons.inventory_2),
          _buildNavItem(2, Icons.people_outline, Icons.people),
          _buildNavItem(3, Icons.analytics_outlined, Icons.analytics),
          _buildNavItem(4, Icons.settings_outlined, Icons.settings),
        ],
      ),
    );
  }

  Widget _buildNavItem(int index, IconData icon, IconData activeIcon) {
    final isActive = currentIndex == index;

    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        onTap(index);
      },
      child: Container(
        padding: const EdgeInsets.all(12),
        child: AnimatedScale(
          scale: isActive ? 1.2 : 1.0,
          duration: const Duration(milliseconds: 200),
          child: Icon(
            isActive ? activeIcon : icon,
            color: isActive ? Colors.white : Colors.white54,
            size: 24,
          ),
        ),
      ),
    );
  }
}

// Style 2: iOS-inspired Tab Bar
class IOSStyleNavBar extends StatelessWidget {
  final int currentIndex;
  final Function(int) onTap;

  const IOSStyleNavBar({
    super.key,
    required this.currentIndex,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.95),
        border: Border(
          top: BorderSide(
            color: Colors.grey.withValues(alpha: 0.2),
            width: 0.5,
          ),
        ),
      ),
      child: SafeArea(
        child: SizedBox(
          height: 90,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildNavItem(
                0,
                Icons.shopping_bag_outlined,
                Icons.shopping_bag,
                'الطلبات',
              ),
              _buildNavItem(
                1,
                Icons.inventory_2_outlined,
                Icons.inventory_2,
                'المنتجات',
              ),
              _buildNavItem(2, Icons.people_outline, Icons.people, 'العملاء'),
              _buildNavItem(
                3,
                Icons.analytics_outlined,
                Icons.analytics,
                'التقارير',
              ),
              _buildNavItem(
                4,
                Icons.settings_outlined,
                Icons.settings,
                'الإعدادات',
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNavItem(
    int index,
    IconData icon,
    IconData activeIcon,
    String label,
  ) {
    final isActive = currentIndex == index;

    return GestureDetector(
      onTap: () {
        HapticFeedback.selectionClick();
        onTap(index);
      },
      behavior: HitTestBehavior.opaque,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              isActive ? activeIcon : icon,
              color: isActive ? const Color(0xFF8B5CF6) : Colors.grey[400],
              size: 26,
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 10,
                fontWeight: FontWeight.w500,
                color: isActive ? const Color(0xFF8B5CF6) : Colors.grey[400],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// Style 3: Material 3 Navigation Bar
class Material3NavBar extends StatelessWidget {
  final int currentIndex;
  final Function(int) onTap;

  const Material3NavBar({
    super.key,
    required this.currentIndex,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return NavigationBar(
      selectedIndex: currentIndex,
      onDestinationSelected: onTap,
      backgroundColor: Colors.white,
      surfaceTintColor: Colors.white,
      indicatorColor: const Color(0xFF8B5CF6).withValues(alpha: 0.15),
      height: 80,
      labelBehavior: NavigationDestinationLabelBehavior.alwaysShow,
      destinations: const [
        NavigationDestination(
          icon: Icon(Icons.shopping_bag_outlined),
          selectedIcon: Icon(Icons.shopping_bag, color: Color(0xFF8B5CF6)),
          label: 'الطلبات',
        ),
        NavigationDestination(
          icon: Icon(Icons.inventory_2_outlined),
          selectedIcon: Icon(Icons.inventory_2, color: Color(0xFF8B5CF6)),
          label: 'المنتجات',
        ),
        NavigationDestination(
          icon: Icon(Icons.people_outline),
          selectedIcon: Icon(Icons.people, color: Color(0xFF8B5CF6)),
          label: 'العملاء',
        ),
        NavigationDestination(
          icon: Icon(Icons.analytics_outlined),
          selectedIcon: Icon(Icons.analytics, color: Color(0xFF8B5CF6)),
          label: 'التقارير',
        ),
        NavigationDestination(
          icon: Icon(Icons.settings_outlined),
          selectedIcon: Icon(Icons.settings, color: Color(0xFF8B5CF6)),
          label: 'الإعدادات',
        ),
      ],
    );
  }
}

// Style 4: Animated Pill Navigation
class PillNavBar extends StatelessWidget {
  final int currentIndex;
  final Function(int) onTap;

  const PillNavBar({
    super.key,
    required this.currentIndex,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      child: SafeArea(
        child: Container(
          height: 90,
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _buildNavItem(
                0,
                Icons.shopping_bag_outlined,
                Icons.shopping_bag,
                'الطلبات',
              ),
              _buildNavItem(
                1,
                Icons.inventory_2_outlined,
                Icons.inventory_2,
                'المنتجات',
              ),
              _buildNavItem(2, Icons.people_outline, Icons.people, 'العملاء'),
              _buildNavItem(
                3,
                Icons.analytics_outlined,
                Icons.analytics,
                'التقارير',
              ),
              _buildNavItem(
                4,
                Icons.settings_outlined,
                Icons.settings,
                'الإعدادات',
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNavItem(
    int index,
    IconData icon,
    IconData activeIcon,
    String label,
  ) {
    final isActive = currentIndex == index;

    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        onTap(index);
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        padding: EdgeInsets.symmetric(
          horizontal: isActive ? 20 : 12,
          vertical: 8,
        ),
        decoration: BoxDecoration(
          color: isActive ? const Color(0xFF8B5CF6) : Colors.transparent,
          borderRadius: BorderRadius.circular(25),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              isActive ? activeIcon : icon,
              color: isActive ? Colors.white : Colors.grey[400],
              size: 22,
            ),
            if (isActive) ...[
              const SizedBox(width: 8),
              Text(
                label,
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
