import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:myrunway/core/constants/app_colors.dart';
import 'package:myrunway/core/models/order_model_new.dart';
import 'package:myrunway/core/models/user_model.dart';
import 'package:myrunway/pages/orders/orders_list_controller.dart';

class OrdersFilterWidget extends StatelessWidget {
  final OrdersListController controller;

  const OrdersFilterWidget({
    super.key,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return Obx(() => Card(
          margin: const EdgeInsets.all(16),
          child: Column(
            children: [
              // Filter header with expand/collapse button
              InkWell(
                onTap: controller.toggleFiltersExpanded,
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      Icon(
                        Icons.filter_list,
                        color: AppColors.primary,
                      ),
                      const SizedBox(width: 8),
                      const Text(
                        'تصفية الطلبات',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const Spacer(),
                      if (controller.hasActiveFilters)
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: AppColors.primary,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Text(
                            'مُفعل',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                            ),
                          ),
                        ),
                      const SizedBox(width: 8),
                      Icon(
                        controller.filtersExpanded
                            ? Icons.expand_less
                            : Icons.expand_more,
                        color: AppColors.primary,
                      ),
                    ],
                  ),
                ),
              ),

              // Filter content (expandable)
              if (controller.filtersExpanded) ...[
                const Divider(height: 1),
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Quick date filters
                      _buildQuickDateFilters(),
                      const SizedBox(height: 16),

                      // Status filter
                      _buildStatusFilter(),
                      const SizedBox(height: 16),

                      // Employee filter (only for managers/masters)
                      if (controller.canViewAllOrders) ...[
                        _buildEmployeeFilter(),
                        const SizedBox(height: 16),
                      ],

                      // Custom date range
                      _buildCustomDateRange(),
                      const SizedBox(height: 16),

                      // Action buttons
                      _buildActionButtons(),
                    ],
                  ),
                ),
              ],
            ],
          ),
        ));
  }

  Widget _buildQuickDateFilters() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'فترات زمنية سريعة',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            _buildQuickFilterChip('اليوم', controller.setTodayFilter),
            _buildQuickFilterChip('أمس', controller.setYesterdayFilter),
            _buildQuickFilterChip('غداً', controller.setTomorrowFilter),
            _buildQuickFilterChip('هذا الأسبوع', controller.setThisWeekFilter),
            _buildQuickFilterChip('الأسبوع الماضي', controller.setLastWeekFilter),
            _buildQuickFilterChip('هذا الشهر', controller.setThisMonthFilter),
            _buildQuickFilterChip('الشهر الماضي', controller.setLastMonthFilter),
          ],
        ),
      ],
    );
  }

  Widget _buildQuickFilterChip(String label, VoidCallback onTap) {
    return ActionChip(
      label: Text(label),
      onPressed: onTap,
      backgroundColor: Colors.grey[100],
      labelStyle: const TextStyle(fontSize: 12),
    );
  }

  Widget _buildStatusFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'حالة الطلب',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<OrderHandlingStatus>(
          value: controller.selectedStatus,
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
            hintText: 'اختر حالة الطلب...',
            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
          items: OrderHandlingStatus.values
              .map((status) => DropdownMenuItem<OrderHandlingStatus>(
                    value: status,
                    child: Text(status.displayName),
                  ))
              .toList(),
          onChanged: controller.setStatusFilter,
        ),
      ],
    );
  }

  Widget _buildEmployeeFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'الموظف المُعيَّن',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<UserModel>(
          value: controller.selectedEmployee,
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
            hintText: 'اختر موظف...',
            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
          items: controller.employees
              .map((employee) => DropdownMenuItem<UserModel>(
                    value: employee,
                    child: Text(employee.fullName),
                  ))
              .toList(),
          onChanged: controller.setEmployeeFilter,
        ),
      ],
    );
  }

  Widget _buildCustomDateRange() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'نطاق تاريخ مخصص',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: InkWell(
                onTap: () => _showDateRangePicker(),
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 16,
                  ),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.date_range, color: Colors.grey[600]),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          _getDateRangeText(),
                          style: TextStyle(
                            color: controller.dateFrom != null || controller.dateTo != null
                                ? Colors.black
                                : Colors.grey[600],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton.icon(
            onPressed: controller.clearFilters,
            icon: const Icon(Icons.clear),
            label: const Text('مسح الفلاتر'),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: ElevatedButton.icon(
            onPressed: controller.applyFilters,
            icon: const Icon(Icons.search),
            label: const Text('تطبيق'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
            ),
          ),
        ),
      ],
    );
  }

  String _getDateRangeText() {
    if (controller.dateFrom != null && controller.dateTo != null) {
      final from = _formatDate(controller.dateFrom!);
      final to = _formatDate(controller.dateTo!);
      return '$from - $to';
    } else if (controller.dateFrom != null) {
      return 'من ${_formatDate(controller.dateFrom!)}';
    } else if (controller.dateTo != null) {
      return 'إلى ${_formatDate(controller.dateTo!)}';
    }
    return 'اختر نطاق التاريخ...';
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  Future<void> _showDateRangePicker() async {
    final DateTimeRange? range = await showDateRangePicker(
      context: Get.context!,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      initialDateRange: controller.dateFrom != null && controller.dateTo != null
          ? DateTimeRange(start: controller.dateFrom!, end: controller.dateTo!)
          : null,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: AppColors.primary,
              onPrimary: Colors.white,
              onSurface: Colors.black,
            ),
          ),
          child: child!,
        );
      },
    );

    if (range != null) {
      controller.setDateFilter(range.start, range.end);
    }
  }
}
