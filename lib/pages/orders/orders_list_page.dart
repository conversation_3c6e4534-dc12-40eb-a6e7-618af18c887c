import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:myrunway/pages/orders/orders_list_controller.dart';
import 'package:myrunway/core/models/order_model_new.dart';
import 'package:myrunway/core/constants/app_colors.dart';
import 'package:myrunway/pages/orders/order_create_page.dart';
import 'package:myrunway/pages/orders/order_edit_page.dart';
import 'package:myrunway/pages/orders/order_details_page.dart';
import 'package:myrunway/widgets/cards/order_card.dart';
import 'package:myrunway/pages/orders/widgets/orders_filter_widget.dart';

class OrdersListPage extends StatelessWidget {
  const OrdersListPage({super.key});

  @override
  Widget build(BuildContext context) {
    final OrdersListController controller = Get.put(OrdersListController());

    return Scaffold(
      appBar: AppBar(
        title: Text(controller.canViewAllOrders ? 'إدارة الطلبات' : 'طلباتي'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          if (controller.canCreateOrders)
            IconButton(
              icon: const Icon(Icons.add),
              onPressed: () => Get.to(() => const OrderCreatePage()),
            ),
        ],
      ),
      body: Obx(() {
        final orders =
            controller.canViewAllOrders
                ? controller.orders
                : controller.myOrders;

        if (controller.isLoading) {
          return Column(
            children: [
              // Show filter widget even when loading
              OrdersFilterWidget(controller: controller),
              const Expanded(child: Center(child: CircularProgressIndicator())),
            ],
          );
        }

        return ListView(
          children: [
            // Filter widget
            OrdersFilterWidget(controller: controller),

            // Orders list
            Expanded(
              child:
                  orders.isEmpty
                      ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.receipt_long,
                              size: 64,
                              color: Colors.grey[400],
                            ),
                            const SizedBox(height: 16),
                            Text(
                              controller.hasActiveFilters
                                  ? 'لا توجد طلبات تطابق الفلاتر المحددة'
                                  : controller.canViewAllOrders
                                  ? 'لا توجد طلبات'
                                  : 'لا توجد طلبات مُعيَّنة لك',
                              style: TextStyle(
                                fontSize: 18,
                                color: Colors.grey[600],
                              ),
                              textAlign: TextAlign.center,
                            ),
                            const SizedBox(height: 16),
                            if (controller.hasActiveFilters)
                              ElevatedButton.icon(
                                onPressed: controller.clearFilters,
                                icon: const Icon(Icons.clear),
                                label: const Text('مسح الفلاتر'),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: AppColors.primary,
                                  foregroundColor: Colors.white,
                                ),
                              )
                            else if (controller.canCreateOrders)
                              ElevatedButton.icon(
                                onPressed:
                                    () => Get.to(() => const OrderCreatePage()),
                                icon: const Icon(Icons.add),
                                label: const Text('إضافة طلب جديد'),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: AppColors.primary,
                                  foregroundColor: Colors.white,
                                ),
                              ),
                          ],
                        ),
                      )
                      : RefreshIndicator(
                        onRefresh: controller.refreshOrders,
                        child: ListView.builder(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          padding: const EdgeInsets.all(16),
                          itemCount: orders.length,
                          itemBuilder: (context, index) {
                            final order = orders[index];
                            return OrderCard(
                              orderNew: order,
                              onTap: () {
                                if (order.id != null) {
                                  Get.to(
                                    () => OrderDetailsPage(orderId: order.id!),
                                  );
                                }
                              },
                              config: OrderCardConfig(
                                displayMode: OrderCardDisplayMode.management,
                                showActions:
                                    controller.canEditOrders ||
                                    controller.canDeleteOrders,
                                actionButtons: _buildActionButtons(
                                  order,
                                  controller,
                                ),
                                borderRadius: BorderRadius.circular(12),
                                margin: const EdgeInsets.only(bottom: 12),
                              ),
                            );
                          },
                        ),
                      ),
            ),
          ],
        );
      }),
    );
  }

  /// Build action buttons for order management
  List<Widget> _buildActionButtons(
    OrderModelNew order,
    OrdersListController controller,
  ) {
    List<Widget> buttons = [];

    if (controller.canEditOrders) {
      buttons.add(
        IconButton(
          icon: const Icon(Icons.edit, size: 20),
          onPressed: () => Get.to(() => OrderEditPage(order: order)),
          color: AppColors.primary,
          tooltip: 'تعديل الطلب',
        ),
      );
    }

    if (controller.canDeleteOrders) {
      buttons.add(
        IconButton(
          icon: const Icon(Icons.delete, size: 20),
          onPressed: () => _showDeleteDialog(order, controller),
          color: Colors.red,
          tooltip: 'حذف الطلب',
        ),
      );
    }

    return buttons;
  }

  /// Show delete confirmation dialog
  void _showDeleteDialog(OrderModelNew order, OrdersListController controller) {
    Get.dialog(
      AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف الطلب "#${order.code}"؟'),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text('إلغاء')),
          TextButton(
            onPressed: () {
              Get.back();
              if (order.id != null) {
                controller.deleteOrder(order.id!);
              }
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }
}
