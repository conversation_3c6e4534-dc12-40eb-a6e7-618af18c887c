import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:myrunway/core/services/auth_service.dart';
import 'package:myrunway/core/constants/user_roles.dart';
import 'package:myrunway/pages/employee/home_page.dart';
import 'package:myrunway/pages/employee/employee_performance_page.dart';
import 'package:myrunway/pages/employee/home_controller.dart';
import 'package:myrunway/pages/auth/login_page.dart';

class EmployeeEntryController extends GetxController {
  final AuthService _authService = Get.find<AuthService>();

  // Bottom navigation
  final RxInt currentIndex = 0.obs;

  // Getters
  bool get isEmployee => _authService.currentUser?.role == UserRole.employee;

  // Pages for the bottom navigation
  List<Widget> get pages => [
    // Employee Orders List Page (main page)
    const EmployeeHomePage(),
    // Employee Reports Page
    _buildReportsPage(),
    // Employee Profile Page
    _buildProfilePage(),
    // Employee Settings Page
    _buildSettingsPage(),
  ];

  @override
  void onInit() {
    super.onInit();
    // Check if user is employee
    if (!isEmployee) {
      Get.snackbar(
        'خطأ في الصلاحية',
        'ليس لديك صلاحية للوصول إلى هذه الصفحة',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }
  }

  // Bottom navigation methods
  void changePage(int index) {
    currentIndex.value = index;
  }

  // Page builders for bottom navigation
  Widget _buildReportsPage() {
    return const EmployeePerformancePage();
  }

  Widget _buildProfilePage() {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الملف الشخصي'),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: Obx(
        () => Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              // User Info Card
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.05),
                      blurRadius: 10,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    CircleAvatar(
                      radius: 40,
                      backgroundColor: Colors.blue.shade100,
                      child: Text(
                        _authService.currentUser?.firstName
                                ?.substring(0, 1)
                                .toUpperCase() ??
                            'M',
                        style: const TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Colors.blue,
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      '${_authService.currentUser?.firstName ?? ''} ${_authService.currentUser?.lastName ?? ''}',
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _authService.currentUser?.role.name ?? 'موظف',
                      style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _authService.currentUser?.username ?? '',
                      style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                    ),
                    if (_authService.currentUser?.office?.name.isNotEmpty ==
                        true) ...[
                      const SizedBox(height: 4),
                      Text(
                        _authService.currentUser!.office!.name,
                        style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                      ),
                    ],
                  ],
                ),
              ),

              const SizedBox(height: 20),

              // Statistics Cards - We'll get these from the home controller
              _buildStatsSection(),

              const Spacer(),

              // Logout Button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () async {
                    await _authService.logout();
                    Get.offAll(() => const LoginPage());
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: const Text('تسجيل الخروج'),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSettingsPage() {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الإعدادات'),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.settings_outlined, size: 80, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'إعدادات التطبيق',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'سيتم تطوير هذه الصفحة قريباً',
              style: TextStyle(fontSize: 14, color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatsSection() {
    // Try to get the home controller if it exists to show stats
    try {
      final homeController = Get.find<EmployeeHomeController>();
      return Obx(() {
        final stats = homeController.employeeStats.value;
        return Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'الطلبات المعينة',
                    stats?.totalOrdersAssigned.toString() ?? '0',
                    Icons.assignment,
                    Colors.blue,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    'الطلبات المكتملة',
                    stats?.totalOrdersCompleted.toString() ?? '0',
                    Icons.check_circle,
                    Colors.green,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'معدل الإنجاز',
                    stats?.formattedCompletionRate ?? '0.0%',
                    Icons.trending_up,
                    Colors.orange,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    'إجمالي العمولة',
                    stats?.formattedTotalMoneyDeserved ?? '0.00 ر.س',
                    Icons.monetization_on,
                    Colors.purple,
                  ),
                ),
              ],
            ),
          ],
        );
      });
    } catch (e) {
      // If home controller is not available, show placeholder
      return Column(
        children: [
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'الطلبات المعينة',
                  '0',
                  Icons.assignment,
                  Colors.blue,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'الطلبات المكتملة',
                  '0',
                  Icons.check_circle,
                  Colors.green,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'معدل الإنجاز',
                  '0.0%',
                  Icons.trending_up,
                  Colors.orange,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'إجمالي العمولة',
                  '0.00 ر.س',
                  Icons.monetization_on,
                  Colors.purple,
                ),
              ),
            ],
          ),
        ],
      );
    }
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(fontSize: 12, color: Colors.grey[600]),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
