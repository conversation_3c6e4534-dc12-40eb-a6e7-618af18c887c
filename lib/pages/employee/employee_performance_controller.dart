import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:myrunway/core/models/employee_performance_models.dart';
import 'package:myrunway/core/services/auth_service.dart';
import 'package:myrunway/core/services/employee_service.dart';
import 'package:myrunway/core/constants/user_roles.dart';

class EmployeePerformanceController extends GetxController {
  final EmployeeService _employeeService = Get.find<EmployeeService>();
  final AuthService _authService = Get.find<AuthService>();

  // Loading states
  final RxBool isLoading = false.obs;
  final RxBool isRefreshing = false.obs;
  final RxString error = ''.obs;

  // Data
  final Rx<EmployeePerformanceModel?> performanceData =
      Rx<EmployeePerformanceModel?>(null);

  // Filter states
  final Rx<DateTime?> dateFrom = Rx<DateTime?>(null);
  final Rx<DateTime?> dateTo = Rx<DateTime?>(null);
  final RxString activeQuickFilter = 'آخر 30 يوم'.obs;

  // Getters
  int? get currentEmployeeId => _authService.currentUser?.id;
  bool get isEmployee => _authService.currentUser?.role == UserRole.employee;
  bool get hasError => error.value.isNotEmpty;
  bool get hasData => performanceData.value != null;

  @override
  void onInit() {
    super.onInit();
    // Check if user is employee
    if (!isEmployee) {
      Get.snackbar(
        'خطأ في الصلاحية',
        'ليس لديك صلاحية للوصول إلى هذه الصفحة',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    // Set default date range (last 30 days)
    setQuickFilter('آخر 30 يوم');
    loadPerformanceData();
  }

  // Load performance data
  Future<void> loadPerformanceData() async {
    if (currentEmployeeId == null) {
      error.value = 'معرف الموظف غير صحيح';
      return;
    }

    isLoading.value = true;
    error.value = '';

    final response = await _employeeService.getEmployeePerformance(
      currentEmployeeId!,
      startDate: dateFrom.value,
      endDate: dateTo.value,
    );

    if (response.success && response.data != null) {
      performanceData.value = response.data!;
    } else {
      error.value = response.message ?? 'فشل في جلب بيانات الأداء';
      Get.snackbar(
        'خطأ',
        error.value,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }

    isLoading.value = false;
  }

  // Refresh data
  Future<void> refreshData() async {
    isRefreshing.value = true;
    await loadPerformanceData();
    isRefreshing.value = false;
  }

  // Quick filter methods
  void setQuickFilter(String filterName) {
    activeQuickFilter.value = filterName;
    final now = DateTime.now();

    switch (filterName) {
      case 'اليوم':
        dateFrom.value = DateTime(now.year, now.month, now.day);
        dateTo.value = now;
        break;
      case 'أمس':
        final yesterday = now.subtract(const Duration(days: 1));
        dateFrom.value = DateTime(
          yesterday.year,
          yesterday.month,
          yesterday.day,
        );
        dateTo.value = DateTime(
          yesterday.year,
          yesterday.month,
          yesterday.day,
          23,
          59,
          59,
        );
        break;
      case 'هذا الأسبوع':
        final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
        dateFrom.value = DateTime(
          startOfWeek.year,
          startOfWeek.month,
          startOfWeek.day,
        );
        dateTo.value = now;
        break;
      case 'الأسبوع الماضي':
        final lastWeekEnd = now.subtract(Duration(days: now.weekday));
        final lastWeekStart = lastWeekEnd.subtract(const Duration(days: 6));
        dateFrom.value = DateTime(
          lastWeekStart.year,
          lastWeekStart.month,
          lastWeekStart.day,
        );
        dateTo.value = DateTime(
          lastWeekEnd.year,
          lastWeekEnd.month,
          lastWeekEnd.day,
          23,
          59,
          59,
        );
        break;
      case 'هذا الشهر':
        dateFrom.value = DateTime(now.year, now.month, 1);
        dateTo.value = now;
        break;
      case 'الشهر الماضي':
        final lastMonth = DateTime(now.year, now.month - 1, 1);
        final lastMonthEnd = DateTime(now.year, now.month, 0);
        dateFrom.value = lastMonth;
        dateTo.value = DateTime(
          lastMonthEnd.year,
          lastMonthEnd.month,
          lastMonthEnd.day,
          23,
          59,
          59,
        );
        break;
      case 'آخر 7 أيام':
        dateFrom.value = now.subtract(const Duration(days: 7));
        dateTo.value = now;
        break;
      case 'آخر 30 يوم':
        dateFrom.value = now.subtract(const Duration(days: 30));
        dateTo.value = now;
        break;
      default:
        // Default to last 30 days
        dateFrom.value = now.subtract(const Duration(days: 30));
        dateTo.value = now;
        break;
    }

    loadPerformanceData();
  }

  // Custom date range
  Future<void> setCustomDateRange() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: Get.context!,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange:
          dateFrom.value != null && dateTo.value != null
              ? DateTimeRange(start: dateFrom.value!, end: dateTo.value!)
              : null,
      locale: const Locale('ar', 'SA'),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(
              context,
            ).colorScheme.copyWith(primary: Colors.deepPurple),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      dateFrom.value = picked.start;
      dateTo.value = picked.end;
      activeQuickFilter.value = 'نطاق مخصص';
      loadPerformanceData();
    }
  }

  // Clear filters
  void clearFilters() {
    setQuickFilter('آخر 30 يوم');
  }

  // Get formatted date range string
  String get formattedDateRange {
    if (dateFrom.value == null || dateTo.value == null) {
      return 'غير محدد';
    }

    final from = dateFrom.value!;
    final to = dateTo.value!;

    if (activeQuickFilter.value == 'نطاق مخصص') {
      return '${from.day}/${from.month}/${from.year} - ${to.day}/${to.month}/${to.year}';
    }

    return activeQuickFilter.value;
  }

  // Performance indicators
  bool get isHighPerformer {
    if (performanceData.value == null) return false;
    return performanceData.value!.isHighPerformer;
  }

  bool get needsImprovement {
    if (performanceData.value == null) return false;
    return performanceData.value!.needsImprovement;
  }

  // Get performance trend indicator
  String get performanceTrendText {
    if (performanceData.value == null) return '';

    final trend = performanceData.value!.recentPerformanceTrend;
    if (trend > 0.05) {
      return 'تحسن في الأداء';
    } else if (trend < -0.05) {
      return 'انخفاض في الأداء';
    } else {
      return 'أداء مستقر';
    }
  }

  Color get performanceTrendColor {
    if (performanceData.value == null) return Colors.grey;

    final trend = performanceData.value!.recentPerformanceTrend;
    if (trend > 0.05) {
      return Colors.green;
    } else if (trend < -0.05) {
      return Colors.red;
    } else {
      return Colors.orange;
    }
  }

  IconData get performanceTrendIcon {
    if (performanceData.value == null) return Icons.trending_flat;

    final trend = performanceData.value!.recentPerformanceTrend;
    if (trend > 0.05) {
      return Icons.trending_up;
    } else if (trend < -0.05) {
      return Icons.trending_down;
    } else {
      return Icons.trending_flat;
    }
  }

  // Quick filter options
  List<String> get quickFilterOptions => [
    'اليوم',
    'أمس',
    'هذا الأسبوع',
    'الأسبوع الماضي',
    'هذا الشهر',
    'الشهر الماضي',
    'آخر 7 أيام',
    'آخر 30 يوم',
  ];
}
