import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:myrunway/pages/employee/employee_entry_controller.dart';
import 'package:myrunway/core/constants/app_colors.dart';
import 'package:myrunway/core/constants/app_strings.dart';
import 'package:myrunway/widgets/drawers/main_drawer.dart';

class EmployeeEntryPage extends StatelessWidget {
  const EmployeeEntryPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(EmployeeEntryController());
    return Scaffold(
      backgroundColor: AppColors.background,
      drawer: const MainDrawer(),
      body: Obx(
        () => IndexedStack(
          index: controller.currentIndex.value,
          children: controller.pages,
        ),
      ),
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 20,
              offset: const Offset(0, -5),
            ),
          ],
        ),
        child: Safe<PERSON><PERSON>(
          child: Container(
            height: 80,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildNavItem(
                  0,
                  Icons.shopping_bag_outlined,
                  Icons.shopping_bag,
                  AppStrings.orders,
                ),
                _buildNavItem(
                  1,
                  Icons.analytics_outlined,
                  Icons.analytics,
                  AppStrings.reports,
                ),
                _buildNavItem(
                  2,
                  Icons.person_outline,
                  Icons.person,
                  'الملف الشخصي',
                ),
                _buildNavItem(
                  3,
                  Icons.settings_outlined,
                  Icons.settings,
                  AppStrings.settings,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildNavItem(
    int index,
    IconData icon,
    IconData activeIcon,
    String label,
  ) {
    final controller = Get.find<EmployeeEntryController>();
    return Obx(() {
      final isActive = controller.currentIndex.value == index;

      return GestureDetector(
        onTap: () {
          HapticFeedback.lightImpact();
          controller.changePage(index);
        },
        behavior: HitTestBehavior.opaque,
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color:
                isActive
                    ? AppColors.primary.withValues(alpha: 0.1)
                    : Colors.transparent,
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              AnimatedSwitcher(
                duration: const Duration(milliseconds: 200),
                child: Icon(
                  isActive ? activeIcon : icon,
                  key: ValueKey(isActive),
                  color: isActive ? AppColors.primary : AppColors.grey400,
                  size: 24,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                label,
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: isActive ? FontWeight.w600 : FontWeight.w400,
                  color: isActive ? AppColors.primary : AppColors.grey400,
                ),
              ),
            ],
          ),
        ),
      );
    });
  }
}
