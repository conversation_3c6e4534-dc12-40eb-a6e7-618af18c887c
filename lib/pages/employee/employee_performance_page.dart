import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:myrunway/pages/employee/employee_performance_controller.dart';
import 'package:myrunway/core/constants/app_colors.dart';
import 'package:myrunway/widgets/cards/info_card.dart';

class EmployeePerformancePage extends StatelessWidget {
  const EmployeePerformancePage({super.key});

  @override
  Widget build(BuildContext context) {
    final EmployeePerformanceController controller = Get.put(
      EmployeePerformanceController(),
    );

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('تقاريري'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: controller.refreshData,
          ),
        ],
      ),
      body: Obx(() {
        if (controller.isLoading.value) {
          return const Center(child: CircularProgressIndicator());
        }

        if (controller.hasError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error_outline, size: 64, color: Colors.red[300]),
                const SizedBox(height: 16),
                Text(
                  controller.error.value,
                  style: const TextStyle(fontSize: 16),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: controller.loadPerformanceData,
                  child: const Text('إعادة المحاولة'),
                ),
              ],
            ),
          );
        }

        return RefreshIndicator(
          onRefresh: controller.refreshData,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Welcome Section
                _buildWelcomeSection(controller),
                const SizedBox(height: 24),

                // Date Filter Controls
                _buildDateFilterControls(controller),
                const SizedBox(height: 24),

                // Key Metrics Section
                if (controller.hasData) _buildKeyMetricsSection(controller),
                if (controller.hasData) const SizedBox(height: 24),

                // Performance Indicators Section
                if (controller.hasData)
                  _buildPerformanceIndicatorsSection(controller),
                if (controller.hasData) const SizedBox(height: 24),

                // Order Status Breakdown
                if (controller.hasData) _buildOrderStatusSection(controller),
                if (controller.hasData) const SizedBox(height: 24),

                // Daily Performance Chart
                if (controller.hasData)
                  _buildDailyPerformanceSection(controller),

                // No data message
                if (!controller.hasData && !controller.isLoading.value)
                  _buildNoDataMessage(),
              ],
            ),
          ),
        );
      }),
    );
  }

  Widget _buildWelcomeSection(EmployeePerformanceController controller) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Row(
          children: [
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: AppColors.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(30),
              ),
              child: Icon(Icons.analytics, color: AppColors.primary, size: 30),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'تقرير الأداء الشخصي',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'تتبع أداءك وإنجازاتك',
                    style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                  ),
                  if (controller.hasData) ...[
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Icon(
                          controller.performanceTrendIcon,
                          color: controller.performanceTrendColor,
                          size: 16,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          controller.performanceTrendText,
                          style: TextStyle(
                            fontSize: 12,
                            color: controller.performanceTrendColor,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDateFilterControls(EmployeePerformanceController controller) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.date_range, color: AppColors.primary),
                const SizedBox(width: 8),
                const Text(
                  'فترة التقرير',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                const Spacer(),
                Text(
                  controller.formattedDateRange,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Quick filter buttons
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children:
                  controller.quickFilterOptions.map((filter) {
                    final isActive =
                        controller.activeQuickFilter.value == filter;
                    return InkWell(
                      onTap: () => controller.setQuickFilter(filter),
                      borderRadius: BorderRadius.circular(20),
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 6,
                        ),
                        decoration: BoxDecoration(
                          color:
                              isActive ? AppColors.primary : Colors.grey[100],
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(
                            color:
                                isActive
                                    ? AppColors.primary
                                    : Colors.grey[300]!,
                          ),
                        ),
                        child: Text(
                          filter,
                          style: TextStyle(
                            color: isActive ? Colors.white : Colors.grey[700],
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    );
                  }).toList(),
            ),
            const SizedBox(height: 12),

            // Custom date range button
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: controller.setCustomDateRange,
                icon: const Icon(Icons.calendar_today, size: 16),
                label: const Text('اختيار نطاق مخصص'),
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildKeyMetricsSection(EmployeePerformanceController controller) {
    final data = controller.performanceData.value!;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'المؤشرات الرئيسية',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),

        // First row
        Row(
          children: [
            Expanded(
              child: StatCard(
                title: 'الطلبات المكتملة',
                value: data.totalOrdersCompleted.toString(),
                icon: Icons.check_circle,
                color: Colors.green,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: StatCard(
                title: 'إجمالي الطلبات',
                value: data.totalOrdersAssigned.toString(),
                icon: Icons.assignment,
                color: AppColors.primary,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),

        // Second row
        Row(
          children: [
            Expanded(
              child: StatCard(
                title: 'إجمالي الإيرادات',
                value: data.formattedTotalRevenue,
                icon: Icons.monetization_on,
                color: Colors.blue,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: StatCard(
                title: 'العمولة المكتسبة',
                value: data.formattedCommissionEarned,
                icon: Icons.payment,
                color: Colors.orange,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildPerformanceIndicatorsSection(
    EmployeePerformanceController controller,
  ) {
    final data = controller.performanceData.value!;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'مؤشرات الأداء',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),

        Row(
          children: [
            Expanded(
              child: StatCard(
                title: 'معدل الإنجاز',
                value: data.formattedCompletionRate,
                icon: Icons.trending_up,
                color:
                    data.completionRate >= 0.8 ? Colors.green : Colors.orange,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: StatCard(
                title: 'التسليم في الوقت',
                value: data.formattedOnTimeDeliveryPercentage,
                icon: Icons.schedule,
                color:
                    data.onTimeDeliveryPercentage >= 0.8
                        ? Colors.green
                        : Colors.red,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),

        Row(
          children: [
            Expanded(
              child: StatCard(
                title: 'متوسط قيمة الطلب',
                value: data.formattedAverageOrderValue,
                icon: Icons.attach_money,
                color: AppColors.info,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: StatCard(
                title: 'متوسط وقت الإنجاز',
                value: data.formattedAverageCompletionTime,
                icon: Icons.timer,
                color: AppColors.warning,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildOrderStatusSection(EmployeePerformanceController controller) {
    final data = controller.performanceData.value!;

    if (data.ordersByStatus.isEmpty) {
      return const SizedBox.shrink();
    }

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.pie_chart, color: AppColors.primary),
                const SizedBox(width: 8),
                const Text(
                  'توزيع الطلبات حسب الحالة',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...data.ordersByStatus.entries.map(
              (entry) => _buildStatusItem(entry.key, entry.value),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusItem(String status, int count) {
    Color statusColor;
    IconData statusIcon;
    String statusName;

    switch (status.toLowerCase()) {
      case 'pending':
        statusColor = Colors.orange;
        statusIcon = Icons.pending;
        statusName = 'في الانتظار';
        break;
      case 'assigned':
        statusColor = Colors.blue;
        statusIcon = Icons.assignment;
        statusName = 'مُعيَّن';
        break;
      case 'processing':
        statusColor = Colors.purple;
        statusIcon = Icons.work;
        statusName = 'قيد التنفيذ';
        break;
      case 'completed':
        statusColor = Colors.green;
        statusIcon = Icons.check_circle;
        statusName = 'مكتمل';
        break;
      default:
        statusColor = Colors.grey;
        statusIcon = Icons.help;
        statusName = status;
        break;
    }

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Container(
            width: 12,
            height: 12,
            decoration: BoxDecoration(
              color: statusColor,
              borderRadius: BorderRadius.circular(6),
            ),
          ),
          const SizedBox(width: 12),
          Icon(statusIcon, size: 16, color: statusColor),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              statusName,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Text(
            count.toString(),
            style: TextStyle(fontWeight: FontWeight.bold, color: statusColor),
          ),
        ],
      ),
    );
  }

  Widget _buildDailyPerformanceSection(
    EmployeePerformanceController controller,
  ) {
    final data = controller.performanceData.value!;

    if (data.dailyPerformance.isEmpty) {
      return const SizedBox.shrink();
    }

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.show_chart, color: AppColors.primary),
                const SizedBox(width: 8),
                const Text(
                  'الأداء اليومي',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Show last 7 days of performance
            ...data.dailyPerformance
                .take(7)
                .map((daily) => _buildDailyPerformanceItem(daily)),
          ],
        ),
      ),
    );
  }

  Widget _buildDailyPerformanceItem(dynamic daily) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6),
      child: Row(
        children: [
          SizedBox(
            width: 80,
            child: Text(
              daily.formattedDate,
              style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            flex: 2,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.assignment, size: 14, color: Colors.grey[600]),
                    const SizedBox(width: 4),
                    Text(
                      '${daily.completedOrders}/${daily.totalOrders}',
                      style: const TextStyle(fontSize: 12),
                    ),
                  ],
                ),
                const SizedBox(height: 2),
                LinearProgressIndicator(
                  value: daily.completionRate,
                  backgroundColor: Colors.grey[200],
                  valueColor: AlwaysStoppedAnimation<Color>(
                    daily.completionRate >= 0.8 ? Colors.green : Colors.orange,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              daily.formattedRevenue,
              style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w600),
              textAlign: TextAlign.end,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNoDataMessage() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          children: [
            Icon(Icons.analytics_outlined, size: 80, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'لا توجد بيانات أداء',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'لا توجد بيانات أداء متاحة للفترة المحددة',
              style: TextStyle(fontSize: 14, color: Colors.grey[500]),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
