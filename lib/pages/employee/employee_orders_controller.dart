import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:myrunway/core/services/auth_service.dart';
import 'package:myrunway/core/services/order_service.dart';
import 'package:myrunway/core/constants/user_roles.dart';
import 'package:myrunway/core/models/order_model_new.dart';
import 'package:myrunway/pages/employee/employee_order_details_page.dart';

class EmployeeOrdersController extends GetxController {
  final AuthService _authService = Get.find<AuthService>();
  final OrderService _orderService = Get.find<OrderService>();

  // Loading states
  final RxBool isLoading = false.obs;
  final RxBool isLoadingOrders = false.obs;

  // Data
  final RxList<OrderModelNew> allOrders = <OrderModelNew>[].obs;
  final RxList<OrderModelNew> filteredOrders = <OrderModelNew>[].obs;

  // Filter states
  final Rx<DateTime?> dateFrom = Rx<DateTime?>(null);
  final Rx<DateTime?> dateTo = Rx<DateTime?>(null);
  final RxString activeQuickFilter = ''.obs;
  final Rx<OrderHandlingStatus?> selectedStatus = Rx<OrderHandlingStatus?>(
    null,
  );

  // Getters
  int? get currentEmployeeId => _authService.currentUser?.id;
  bool get isEmployee => _authService.currentUser?.role == UserRole.employee;

  @override
  void onInit() {
    super.onInit();
    // Check if user is employee
    if (!isEmployee) {
      Get.snackbar(
        'خطأ في الصلاحية',
        'ليس لديك صلاحية للوصول إلى هذه الصفحة',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      Get.back();
      return;
    }

    loadEmployeeOrders();
  }

  Future<void> loadEmployeeOrders() async {
    if (currentEmployeeId == null) return;

    isLoadingOrders.value = true;

    final response = await _orderService.getOrders(
      dateFrom: dateFrom.value?.toIso8601String().split('T')[0],
      dateTo: dateTo.value?.toIso8601String().split('T')[0],
      status: selectedStatus.value?.name,
      assignedTo: currentEmployeeId,
    );

    if (response.success && response.data != null) {
      allOrders.value = response.data!;
      applyFilters();
    } else {
      Get.snackbar(
        'خطأ',
        response.message ?? 'فشل في جلب الطلبات',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }

    isLoadingOrders.value = false;
  }

  Future<void> refreshData() async {
    await loadEmployeeOrders();
  }

  void applyFilters() {
    List<OrderModelNew> filtered = List.from(allOrders);

    // Apply status filter
    if (selectedStatus.value != null) {
      filtered =
          filtered
              .where(
                (order) => order.orderHandlingStatus == selectedStatus.value,
              )
              .toList();
    }

    // Apply date filters
    if (dateFrom.value != null) {
      filtered =
          filtered
              .where((order) => order.createdAt.isAfter(dateFrom.value!))
              .toList();
    }

    if (dateTo.value != null) {
      final endOfDay = DateTime(
        dateTo.value!.year,
        dateTo.value!.month,
        dateTo.value!.day,
        23,
        59,
        59,
      );
      filtered =
          filtered
              .where((order) => order.createdAt.isBefore(endOfDay))
              .toList();
    }

    // Sort by creation date (newest first)
    filtered.sort((a, b) => b.createdAt.compareTo(a.createdAt));

    filteredOrders.value = filtered;
  }

  // Quick filter methods
  void setTodayFilter() {
    final today = DateTime.now();
    final startOfDay = DateTime(today.year, today.month, today.day);
    final endOfDay = DateTime(today.year, today.month, today.day, 23, 59, 59);
    setDateFilter(startOfDay, endOfDay);
    activeQuickFilter.value = 'اليوم';
  }

  void setYesterdayFilter() {
    final yesterday = DateTime.now().subtract(const Duration(days: 1));
    final startOfDay = DateTime(yesterday.year, yesterday.month, yesterday.day);
    final endOfDay = DateTime(
      yesterday.year,
      yesterday.month,
      yesterday.day,
      23,
      59,
      59,
    );
    setDateFilter(startOfDay, endOfDay);
    activeQuickFilter.value = 'أمس';
  }

  void setTomorrowFilter() {
    final tomorrow = DateTime.now().add(const Duration(days: 1));
    final startOfDay = DateTime(tomorrow.year, tomorrow.month, tomorrow.day);
    final endOfDay = DateTime(
      tomorrow.year,
      tomorrow.month,
      tomorrow.day,
      23,
      59,
      59,
    );
    setDateFilter(startOfDay, endOfDay);
    activeQuickFilter.value = 'غداً';
  }

  void setThisWeekFilter() {
    final now = DateTime.now();
    final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
    final endOfWeek = startOfWeek.add(
      const Duration(days: 6, hours: 23, minutes: 59, seconds: 59),
    );
    setDateFilter(startOfWeek, endOfWeek);
    activeQuickFilter.value = 'هذا الأسبوع';
  }

  void setLastWeekFilter() {
    final now = DateTime.now();
    final startOfLastWeek = now.subtract(Duration(days: now.weekday + 6));
    final endOfLastWeek = startOfLastWeek.add(
      const Duration(days: 6, hours: 23, minutes: 59, seconds: 59),
    );
    setDateFilter(startOfLastWeek, endOfLastWeek);
    activeQuickFilter.value = 'الأسبوع الماضي';
  }

  void setThisMonthFilter() {
    final now = DateTime.now();
    final startOfMonth = DateTime(now.year, now.month, 1);
    final endOfMonth = DateTime(now.year, now.month + 1, 0, 23, 59, 59);
    setDateFilter(startOfMonth, endOfMonth);
    activeQuickFilter.value = 'هذا الشهر';
  }

  void setLastMonthFilter() {
    final now = DateTime.now();
    final startOfLastMonth = DateTime(now.year, now.month - 1, 1);
    final endOfLastMonth = DateTime(now.year, now.month, 0, 23, 59, 59);
    setDateFilter(startOfLastMonth, endOfLastMonth);
    activeQuickFilter.value = 'الشهر الماضي';
  }

  void setDateFilter(DateTime from, DateTime to) {
    dateFrom.value = from;
    dateTo.value = to;
    loadEmployeeOrders();
  }

  void clearFilters() {
    dateFrom.value = null;
    dateTo.value = null;
    selectedStatus.value = null;
    activeQuickFilter.value = '';
    loadEmployeeOrders();
  }

  void setStatusFilter(OrderHandlingStatus? status) {
    selectedStatus.value = status;
    loadEmployeeOrders();
  }

  bool isQuickFilterActive(String filterName) {
    return activeQuickFilter.value == filterName;
  }

  void navigateToOrderDetails(OrderModelNew order) {
    if (order.id != null) {
      Get.to(() => EmployeeOrderDetailsPage(orderId: order.id!));
    }
  }

  void showDateFilterDialog() {
    Get.dialog(
      Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Container(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'فلترة حسب التاريخ',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),
              _buildDateRangePicker(),
              const SizedBox(height: 24),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Get.back(),
                    child: const Text('إلغاء'),
                  ),
                  const SizedBox(width: 8),
                  ElevatedButton(
                    onPressed: () {
                      Get.back();
                      loadEmployeeOrders();
                    },
                    child: const Text('تطبيق'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDateRangePicker() {
    return Obx(
      () => InkWell(
        onTap: _showDateRangePicker,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              Icon(Icons.date_range, color: Colors.grey[600]),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  _getDateRangeText(),
                  style: TextStyle(
                    color:
                        dateFrom.value != null || dateTo.value != null
                            ? Colors.black
                            : Colors.grey[600],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _showDateRangePicker() async {
    final DateTimeRange? range = await showDateRangePicker(
      context: Get.context!,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      initialDateRange:
          dateFrom.value != null && dateTo.value != null
              ? DateTimeRange(start: dateFrom.value!, end: dateTo.value!)
              : null,
    );

    if (range != null) {
      setDateFilter(range.start, range.end);
      activeQuickFilter.value = 'نطاق مخصص';
    }
  }

  String _getDateRangeText() {
    if (dateFrom.value != null && dateTo.value != null) {
      return '${_formatDate(dateFrom.value!)} - ${_formatDate(dateTo.value!)}';
    } else if (dateFrom.value != null) {
      return 'من ${_formatDate(dateFrom.value!)}';
    } else if (dateTo.value != null) {
      return 'إلى ${_formatDate(dateTo.value!)}';
    }
    return 'اختر نطاق التاريخ';
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
