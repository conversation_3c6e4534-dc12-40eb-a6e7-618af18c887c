import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:myrunway/core/services/auth_service.dart';
import 'package:myrunway/core/services/api_service.dart';
import 'package:myrunway/core/constants/api_endpoints.dart';
import 'package:myrunway/core/constants/user_roles.dart';
import 'package:myrunway/core/models/order_model_new.dart';
import 'package:myrunway/core/models/employee_stats_model.dart';
import 'package:myrunway/pages/employee/employee_order_details_page.dart';
import 'package:myrunway/pages/employee/employee_orders_page.dart';
import 'package:myrunway/widgets/employee_date_filter_dialog.dart';

class EmployeeHomeController extends GetxController {
  final AuthService _authService = Get.find<AuthService>();
  final ApiService _apiService = Get.find<ApiService>();

  // Loading states
  final RxBool isLoading = false.obs;
  final RxBool isLoadingStats = false.obs;
  final RxBool isLoadingOrders = false.obs;

  // Data
  final Rx<EmployeeStatsModel?> employeeStats = Rx<EmployeeStatsModel?>(null);
  final RxList<OrderModelNew> allOrders = <OrderModelNew>[].obs;
  final RxList<OrderModelNew> filteredOrders = <OrderModelNew>[].obs;

  // Filter states
  final Rx<DateTime?> dateFrom = Rx<DateTime?>(null);
  final Rx<DateTime?> dateTo = Rx<DateTime?>(null);
  final RxString activeQuickFilter = ''.obs;

  // Getters
  int? get currentEmployeeId => _authService.currentUser?.id;
  bool get isEmployee => _authService.currentUser?.role == UserRole.employee;

  @override
  void onInit() {
    super.onInit();
    // Check if user is employee
    if (!isEmployee) {
      Get.snackbar(
        'خطأ في الصلاحية',
        'ليس لديك صلاحية للوصول إلى هذه الصفحة',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    loadInitialData();
  }

  Future<void> loadInitialData() async {
    isLoading.value = true;
    await Future.wait([loadEmployeeStats(), loadEmployeeOrders()]);
    isLoading.value = false;
  }

  Future<void> refreshData() async {
    await Future.wait([loadEmployeeStats(), loadEmployeeOrders()]);
  }

  // Load employee stats from API
  Future<void> loadEmployeeStats() async {
    if (currentEmployeeId == null) return;

    isLoadingStats.value = true;

    try {
      final response = await _apiService.get<Map<String, dynamic>>(
        ApiEndpoints.employeeStatsById(currentEmployeeId.toString()),
        (data) => data as Map<String, dynamic>,
      );

      if (response.success && response.data != null) {
        employeeStats.value = EmployeeStatsModel.fromJson(response.data!);
      }
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'فشل في تحميل إحصائيات الموظف',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoadingStats.value = false;
    }
  }

  // Load employee orders from API
  Future<void> loadEmployeeOrders() async {
    if (currentEmployeeId == null) return;

    isLoadingOrders.value = true;

    try {
      final Map<String, dynamic> query = {'assigned_to': currentEmployeeId};

      // Add date filters if set
      if (dateFrom.value != null) {
        query['date_from'] = dateFrom.value!.toIso8601String().split('T')[0];
      }
      if (dateTo.value != null) {
        query['date_to'] = dateTo.value!.toIso8601String().split('T')[0];
      }

      final response = await _apiService.get<List<dynamic>>(
        ApiEndpoints.orders,
        (data) => data as List<dynamic>,
        query: query,
      );

      if (response.success && response.data != null) {
        allOrders.value =
            response.data!
                .map(
                  (json) =>
                      OrderModelNew.fromJson(json as Map<String, dynamic>),
                )
                .toList();

        // Apply default filter: show only non-completed orders
        applyDefaultFilter();
      }
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'فشل في تحميل الطلبات',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoadingOrders.value = false;
    }
  }

  // Apply default filter (non-completed orders)
  void applyDefaultFilter() {
    filteredOrders.value =
        allOrders.where((order) {
          return order.orderHandlingStatus != OrderHandlingStatus.completed;
        }).toList();
  }

  // Apply filters based on current filter state
  void applyFilters() {
    List<OrderModelNew> filtered = List.from(allOrders);

    // Apply date filters
    if (dateFrom.value != null || dateTo.value != null) {
      filtered =
          filtered.where((order) {
            final orderDate = order.createdAt;

            if (dateFrom.value != null && orderDate.isBefore(dateFrom.value!)) {
              return false;
            }

            if (dateTo.value != null &&
                orderDate.isAfter(dateTo.value!.add(const Duration(days: 1)))) {
              return false;
            }

            return true;
          }).toList();
    } else {
      // If no date filter, apply default filter (non-completed orders)
      filtered =
          filtered.where((order) {
            return order.orderHandlingStatus != OrderHandlingStatus.completed;
          }).toList();
    }

    filteredOrders.value = filtered;
  }

  // Quick filter methods
  void setTodayFilter() {
    final today = DateTime.now();
    final startOfDay = DateTime(today.year, today.month, today.day);
    final endOfDay = DateTime(today.year, today.month, today.day, 23, 59, 59);
    setDateFilter(startOfDay, endOfDay);
    activeQuickFilter.value = 'اليوم';
  }

  void setYesterdayFilter() {
    final yesterday = DateTime.now().subtract(const Duration(days: 1));
    final startOfDay = DateTime(yesterday.year, yesterday.month, yesterday.day);
    final endOfDay = DateTime(
      yesterday.year,
      yesterday.month,
      yesterday.day,
      23,
      59,
      59,
    );
    setDateFilter(startOfDay, endOfDay);
    activeQuickFilter.value = 'أمس';
  }

  void setTomorrowFilter() {
    final tomorrow = DateTime.now().add(const Duration(days: 1));
    final startOfDay = DateTime(tomorrow.year, tomorrow.month, tomorrow.day);
    final endOfDay = DateTime(
      tomorrow.year,
      tomorrow.month,
      tomorrow.day,
      23,
      59,
      59,
    );
    setDateFilter(startOfDay, endOfDay);
    activeQuickFilter.value = 'غداً';
  }

  void setThisWeekFilter() {
    final now = DateTime.now();
    final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
    final endOfWeek = startOfWeek.add(
      const Duration(days: 6, hours: 23, minutes: 59, seconds: 59),
    );
    setDateFilter(startOfWeek, endOfWeek);
    activeQuickFilter.value = 'هذا الأسبوع';
  }

  void setLastWeekFilter() {
    final now = DateTime.now();
    final startOfLastWeek = now.subtract(Duration(days: now.weekday + 6));
    final endOfLastWeek = startOfLastWeek.add(
      const Duration(days: 6, hours: 23, minutes: 59, seconds: 59),
    );
    setDateFilter(startOfLastWeek, endOfLastWeek);
    activeQuickFilter.value = 'الأسبوع الماضي';
  }

  void setThisMonthFilter() {
    final now = DateTime.now();
    final startOfMonth = DateTime(now.year, now.month, 1);
    final endOfMonth = DateTime(now.year, now.month + 1, 0, 23, 59, 59);
    setDateFilter(startOfMonth, endOfMonth);
    activeQuickFilter.value = 'هذا الشهر';
  }

  void setLastMonthFilter() {
    final now = DateTime.now();
    final startOfLastMonth = DateTime(now.year, now.month - 1, 1);
    final endOfLastMonth = DateTime(now.year, now.month, 0, 23, 59, 59);
    setDateFilter(startOfLastMonth, endOfLastMonth);
    activeQuickFilter.value = 'الشهر الماضي';
  }

  void setDateFilter(DateTime? from, DateTime? to) {
    dateFrom.value = from;
    dateTo.value = to;
    activeQuickFilter.value = '';
    loadEmployeeOrders();
  }

  void clearFilters() {
    dateFrom.value = null;
    dateTo.value = null;
    activeQuickFilter.value = '';
    loadEmployeeOrders();
  }

  bool isQuickFilterActive(String filterName) {
    return activeQuickFilter.value == filterName;
  }

  // Navigation methods
  void navigateToOrderDetails(OrderModelNew order) {
    if (order.id == null) {
      Get.snackbar(
        'خطأ',
        'معرف الطلب غير صحيح',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    Get.to(() => EmployeeOrderDetailsPage(orderId: order.id!));
  }

  void navigateToAllOrders() {
    Get.to(() => const EmployeeOrdersPage());
  }

  void showDateFilterDialog() {
    Get.dialog(EmployeeDateFilterDialog(controller: this));
  }
}
