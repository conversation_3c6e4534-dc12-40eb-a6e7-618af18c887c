import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:myrunway/pages/employee/employee_orders_controller.dart';
import 'package:myrunway/widgets/cards/order_card.dart';
import 'package:myrunway/core/models/order_model_new.dart';

class EmployeeOrdersPage extends StatelessWidget {
  const EmployeeOrdersPage({super.key});

  @override
  Widget build(BuildContext context) {
    final EmployeeOrdersController controller = Get.put(
      EmployeeOrdersController(),
    );

    return Scaffold(
      appBar: AppBar(
        title: const Text('جميع طلباتي'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: controller.refreshData,
          ),
        ],
      ),
      body: Obx(
        () =>
            controller.isLoading.value
                ? const Center(child: CircularProgressIndicator())
                : RefreshIndicator(
                  onRefresh: controller.refreshData,
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Filter Controls
                        _buildFilterControls(controller),

                        const SizedBox(height: 16),

                        // Orders List Section
                        _buildOrdersList(controller),
                      ],
                    ),
                  ),
                ),
      ),
    );
  }

  Widget _buildFilterControls(EmployeeOrdersController controller) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Filter Header
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'فلترة الطلبات',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              TextButton.icon(
                onPressed: controller.clearFilters,
                icon: const Icon(Icons.clear, size: 16),
                label: const Text('مسح الفلاتر'),
                style: TextButton.styleFrom(foregroundColor: Colors.blue),
              ),
            ],
          ),
          const SizedBox(height: 12),

          // Status Filter
          _buildStatusFilter(controller),
          const SizedBox(height: 16),

          // Quick Date Filters
          const Text(
            'فلترة حسب التاريخ',
            style: TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: [
              _buildQuickFilterChip(
                controller,
                'اليوم',
                controller.setTodayFilter,
              ),
              _buildQuickFilterChip(
                controller,
                'أمس',
                controller.setYesterdayFilter,
              ),
              _buildQuickFilterChip(
                controller,
                'غداً',
                controller.setTomorrowFilter,
              ),
              _buildQuickFilterChip(
                controller,
                'هذا الأسبوع',
                controller.setThisWeekFilter,
              ),
              _buildQuickFilterChip(
                controller,
                'الأسبوع الماضي',
                controller.setLastWeekFilter,
              ),
              _buildQuickFilterChip(
                controller,
                'هذا الشهر',
                controller.setThisMonthFilter,
              ),
              _buildQuickFilterChip(
                controller,
                'الشهر الماضي',
                controller.setLastMonthFilter,
              ),
              _buildCustomDateFilterChip(controller),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatusFilter(EmployeeOrdersController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'فلترة حسب الحالة',
          style: TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            _buildStatusChip(controller, null, 'جميع الحالات'),
            _buildStatusChip(
              controller,
              OrderHandlingStatus.pending,
              'في الانتظار',
            ),
            _buildStatusChip(
              controller,
              OrderHandlingStatus.assigned,
              'مُعيَّن',
            ),
            _buildStatusChip(
              controller,
              OrderHandlingStatus.processing,
              'قيد التنفيذ',
            ),
            _buildStatusChip(
              controller,
              OrderHandlingStatus.completed,
              'مكتمل',
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatusChip(
    EmployeeOrdersController controller,
    OrderHandlingStatus? status,
    String label,
  ) {
    return Obx(() {
      final isActive = controller.selectedStatus.value == status;
      return ActionChip(
        label: Text(label),
        onPressed: () => controller.setStatusFilter(status),
        backgroundColor:
            isActive ? Colors.blue.withValues(alpha: 0.1) : Colors.grey[100],
        labelStyle: TextStyle(
          fontSize: 12,
          color: isActive ? Colors.blue : Colors.black87,
          fontWeight: isActive ? FontWeight.w600 : FontWeight.normal,
        ),
        side: BorderSide(
          color: isActive ? Colors.blue : Colors.transparent,
          width: 1,
        ),
      );
    });
  }

  Widget _buildQuickFilterChip(
    EmployeeOrdersController controller,
    String label,
    VoidCallback onTap,
  ) {
    final isActive = controller.isQuickFilterActive(label);
    return ActionChip(
      label: Text(label),
      onPressed: onTap,
      backgroundColor:
          isActive ? Colors.blue.withValues(alpha: 0.1) : Colors.grey[100],
      labelStyle: TextStyle(
        fontSize: 12,
        color: isActive ? Colors.blue : Colors.black87,
        fontWeight: isActive ? FontWeight.w600 : FontWeight.normal,
      ),
      side: BorderSide(
        color: isActive ? Colors.blue : Colors.transparent,
        width: 1,
      ),
    );
  }

  Widget _buildCustomDateFilterChip(EmployeeOrdersController controller) {
    return Obx(() {
      final hasCustomRange =
          controller.dateFrom.value != null || controller.dateTo.value != null;
      return ActionChip(
        label: Text(hasCustomRange ? 'نطاق مخصص' : 'تاريخ مخصص'),
        onPressed: controller.showDateFilterDialog,
        backgroundColor:
            hasCustomRange
                ? Colors.blue.withValues(alpha: 0.1)
                : Colors.grey[100],
        labelStyle: TextStyle(
          fontSize: 12,
          color: hasCustomRange ? Colors.blue : Colors.black87,
          fontWeight: hasCustomRange ? FontWeight.w600 : FontWeight.normal,
        ),
        side: BorderSide(
          color: hasCustomRange ? Colors.blue : Colors.transparent,
          width: 1,
        ),
      );
    });
  }

  Widget _buildOrdersList(EmployeeOrdersController controller) {
    return Obx(() {
      if (controller.isLoadingOrders.value) {
        return const Center(
          child: Padding(
            padding: EdgeInsets.all(32),
            child: CircularProgressIndicator(),
          ),
        );
      }

      if (controller.filteredOrders.isEmpty) {
        return _buildEmptyState();
      }

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'طلباتي (${controller.filteredOrders.length})',
            style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 12),
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: controller.filteredOrders.length,
            itemBuilder: (context, index) {
              final order = controller.filteredOrders[index];
              return OrderCard(
                orderNew: order,
                onTap: () => controller.navigateToOrderDetails(order),
              );
            },
          ),
        ],
      );
    });
  }

  Widget _buildEmptyState() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(32),
      child: Column(
        children: [
          Icon(Icons.assignment_outlined, size: 80, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            'لا توجد طلبات',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'لم يتم العثور على طلبات تطابق الفلاتر المحددة',
            style: TextStyle(fontSize: 14, color: Colors.grey[500]),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
