import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:myrunway/pages/users/users_list_controller.dart';
import 'package:myrunway/core/models/user_model.dart';
import 'package:myrunway/core/constants/app_colors.dart';
import 'package:myrunway/core/constants/user_roles.dart';
import 'package:myrunway/pages/users/user_create_page.dart';
import 'package:myrunway/pages/users/user_edit_page.dart';
import 'package:myrunway/pages/employee_management/employee_management_page.dart';

class UsersListPage extends StatelessWidget {
  const UsersListPage({super.key});

  @override
  Widget build(BuildContext context) {
    final UsersListController controller = Get.put(UsersListController());

    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة المستخدمين'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          if (controller.canCreateUsers)
            IconButton(
              icon: const Icon(Icons.add),
              onPressed: () => Get.to(() => const UserCreatePage()),
            ),
        ],
      ),
      body: Obx(() {
        if (controller.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (controller.users.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.people, size: 64, color: Colors.grey[400]),
                const SizedBox(height: 16),
                Text(
                  'لا يوجد مستخدمون',
                  style: TextStyle(fontSize: 18, color: Colors.grey[600]),
                ),
                const SizedBox(height: 16),
                if (controller.canCreateUsers)
                  ElevatedButton.icon(
                    onPressed: () => Get.to(() => const UserCreatePage()),
                    icon: const Icon(Icons.add),
                    label: const Text('إضافة مستخدم جديد'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: Colors.white,
                    ),
                  ),
              ],
            ),
          );
        }

        return RefreshIndicator(
          onRefresh: controller.refreshUsers,
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: controller.users.length,
            itemBuilder: (context, index) {
              final user = controller.users[index];
              return _UserCard(user: user, controller: controller);
            },
          ),
        );
      }),
    );
  }
}

class _UserCard extends StatelessWidget {
  final UserModel user;
  final UsersListController controller;

  const _UserCard({required this.user, required this.controller});

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () => controller.selectUser(user),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  // Avatar
                  CircleAvatar(
                    radius: 24,
                    backgroundColor: _getRoleColor(user.role),
                    child: Text(
                      user.fullName.isNotEmpty
                          ? user.fullName[0].toUpperCase()
                          : user.username[0].toUpperCase(),
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 18,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          user.fullName.isNotEmpty
                              ? user.fullName
                              : user.username,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          '@${user.username}',
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                  // Role badge
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: _getRoleColor(user.role),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      user.role.displayName,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),

              // Contact info
              if (user.email != null && user.email!.isNotEmpty) ...[
                Row(
                  children: [
                    Icon(Icons.email, size: 16, color: Colors.grey[600]),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        user.email!,
                        style: TextStyle(color: Colors.grey[600], fontSize: 14),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
              ],

              if (user.phone != null && user.phone!.isNotEmpty) ...[
                Row(
                  children: [
                    Icon(Icons.phone, size: 16, color: Colors.grey[600]),
                    const SizedBox(width: 4),
                    Text(
                      user.phone!,
                      style: TextStyle(color: Colors.grey[600], fontSize: 14),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
              ],

              // Office
              if (user.office != null) ...[
                Row(
                  children: [
                    Icon(Icons.business, size: 16, color: Colors.grey[600]),
                    const SizedBox(width: 4),
                    Text(
                      user.office!.name,
                      style: TextStyle(color: Colors.grey[600], fontSize: 14),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
              ],

              // Commission rate for employees
              if (user.isEmployee && user.commissionRate != null) ...[
                Row(
                  children: [
                    Icon(Icons.percent, size: 16, color: Colors.green[600]),
                    const SizedBox(width: 4),
                    Text(
                      'نسبة العمولة: ${user.commissionRate!.toStringAsFixed(1)}%',
                      style: TextStyle(color: Colors.green[600], fontSize: 14),
                    ),
                  ],
                ),
              ],

              const SizedBox(height: 12),
              Row(
                children: [
                  Text(
                    'تاريخ الانضمام: ${_formatDate(user.dateJoined)}',
                    style: TextStyle(color: Colors.grey[600], fontSize: 12),
                  ),
                  const Spacer(),
                  // Employee management button (only for employees)
                  if (user.role == UserRole.employee && controller.canEditUsers)
                    IconButton(
                      icon: const Icon(Icons.analytics, size: 20),
                      onPressed: () => _navigateToEmployeeManagement(),
                      color: Colors.orange,
                      tooltip: 'إدارة الموظف',
                    ),
                  if (controller.canEditUsers)
                    IconButton(
                      icon: const Icon(Icons.edit, size: 20),
                      onPressed: () => Get.to(() => UserEditPage(user: user)),
                      color: AppColors.primary,
                      tooltip: 'تعديل',
                    ),
                  if (controller.canDeleteUsers)
                    IconButton(
                      icon: const Icon(Icons.delete, size: 20),
                      onPressed: () => _showDeleteDialog(context),
                      color: Colors.red,
                      tooltip: 'حذف',
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getRoleColor(UserRole role) {
    switch (role) {
      case UserRole.master:
        return Colors.purple;
      case UserRole.manager:
        return Colors.blue;
      case UserRole.employee:
        return Colors.green;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _navigateToEmployeeManagement() {
    if (user.id != null) {
      Get.to(
        () => const EmployeeManagementPage(),
        arguments: {'employeeId': user.id!, 'employee': user},
      );
    }
  }

  void _showDeleteDialog(BuildContext context) {
    Get.dialog(
      AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text(
          'هل أنت متأكد من حذف المستخدم "${user.fullName.isNotEmpty ? user.fullName : user.username}"؟',
        ),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text('إلغاء')),
          TextButton(
            onPressed: () {
              Get.back();
              if (user.id != null) {
                controller.deleteUser(user.id!);
              }
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }
}
