import 'package:get/get.dart';
import 'package:myrunway/core/models/user_model.dart';
import 'package:myrunway/core/services/auth_service.dart';
import 'package:myrunway/core/services/api_service.dart';
import 'package:myrunway/core/constants/api_endpoints.dart';
import 'package:myrunway/core/constants/user_roles.dart';

class ProfileController extends GetxController {
  final AuthService _authService = Get.find<AuthService>();
  final ApiService _apiService = Get.find<ApiService>();

  // Reactive variables
  final RxBool _isLoading = false.obs;
  final RxBool _isRefreshing = false.obs;
  final Rx<UserModel?> _userProfile = Rx<UserModel?>(null);
  final RxString _error = ''.obs;

  // Getters
  bool get isLoading => _isLoading.value;
  bool get isRefreshing => _isRefreshing.value;
  UserModel? get userProfile => _userProfile.value;
  String get error => _error.value;
  UserModel? get currentUser => _authService.currentUser;

  // Role-based getters
  bool get isEmployee => currentUser?.role == UserRole.employee;
  bool get isManager => currentUser?.role == UserRole.manager;
  bool get isMaster => currentUser?.role == UserRole.master;

  @override
  void onInit() {
    super.onInit();
    loadUserProfile();
  }

  /// Load user profile data
  Future<void> loadUserProfile() async {
    if (_isLoading.value) return;

    _isLoading.value = true;
    _error.value = '';

    try {
      // Use current user data from AuthService as primary source
      if (_authService.currentUser != null) {
        _userProfile.value = _authService.currentUser;
      }

      // Optionally fetch fresh data from API
      await refreshUserProfile();
    } catch (e) {
      _error.value = 'فشل في تحميل بيانات الملف الشخصي';
    } finally {
      _isLoading.value = false;
    }
  }

  /// Refresh user profile from API
  Future<void> refreshUserProfile() async {
    if (_isRefreshing.value) return;

    _isRefreshing.value = true;

    try {
      final response = await _apiService.get<Map<String, dynamic>>(
        ApiEndpoints.getCurrentUser,
        (data) => data as Map<String, dynamic>,
      );

      if (response.success && response.data != null) {
        final updatedUser = UserModel.fromJson(response.data!);
        _userProfile.value = updatedUser;

        _error.value = '';
      } else {
        _error.value = response.message ?? 'فشل في تحديث بيانات الملف الشخصي';
      }
    } catch (e) {
      _error.value = 'خطأ في الاتصال بالخادم';
    } finally {
      _isRefreshing.value = false;
    }
  }

  /// Get formatted join date
  String getFormattedJoinDate() {
    if (userProfile?.dateJoined == null) return 'غير محدد';

    final date = userProfile!.dateJoined;
    return '${date.day}/${date.month}/${date.year}';
  }

  /// Get formatted last location update
  String getFormattedLastLocationUpdate() {
    if (userProfile?.lastLocationUpdate == null) return 'غير محدد';

    final date = userProfile!.lastLocationUpdate!;
    return '${date.day}/${date.month}/${date.year} - ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }

  /// Get commission rate as percentage
  String getCommissionRatePercentage() {
    if (userProfile?.commissionRate == null) return 'غير محدد';
    return '${(userProfile!.commissionRate! * 100).toStringAsFixed(1)}%';
  }

  /// Get office name
  String getOfficeName() {
    return userProfile?.office?.name ?? 'غير محدد';
  }

  /// Get office address
  String getOfficeAddress() {
    return userProfile?.office?.address ?? 'غير محدد';
  }

  /// Check if user has location data
  bool hasLocationData() {
    return userProfile?.currentLocationLat != null &&
        userProfile?.currentLocationLng != null;
  }

  /// Get location coordinates as string
  String getLocationCoordinates() {
    if (!hasLocationData()) return 'غير محدد';

    return 'خط العرض: ${userProfile!.currentLocationLat!.toStringAsFixed(6)}\n'
        'خط الطول: ${userProfile!.currentLocationLng!.toStringAsFixed(6)}';
  }

  /// Navigate to edit profile (placeholder for future implementation)
  void navigateToEditProfile() {
    Get.snackbar(
      'قريباً',
      'ستتوفر ميزة تعديل الملف الشخصي قريباً',
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  /// Navigate to change password (placeholder for future implementation)
  void navigateToChangePassword() {
    Get.snackbar(
      'قريباً',
      'ستتوفر ميزة تغيير كلمة المرور قريباً',
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  /// Logout user
  Future<void> logout() async {
    await _authService.logout();
  }
}
