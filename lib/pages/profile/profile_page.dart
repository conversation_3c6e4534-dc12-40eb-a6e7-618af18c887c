import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:myrunway/pages/profile/profile_controller.dart';
import 'package:myrunway/core/constants/app_colors.dart';

import 'package:myrunway/widgets/cards/info_card.dart';
import 'package:myrunway/widgets/buttons/primary_button.dart';

class ProfilePage extends StatelessWidget {
  const ProfilePage({super.key});

  @override
  Widget build(BuildContext context) {
    final ProfileController controller = Get.put(ProfileController());

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text(
          'الملف الشخصي',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        backgroundColor: AppColors.background,
        elevation: 0,
        leading: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: () => Get.back(),
            borderRadius: BorderRadius.circular(20),
            child: const Icon(Icons.arrow_back, color: AppColors.textPrimary),
          ),
        ),
        actions: [
          Obx(
            () => Material(
              color: Colors.transparent,
              child: InkWell(
                onTap:
                    controller.isRefreshing
                        ? null
                        : controller.refreshUserProfile,
                borderRadius: BorderRadius.circular(20),
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child:
                      controller.isRefreshing
                          ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                AppColors.primary,
                              ),
                            ),
                          )
                          : const Icon(
                            Icons.refresh,
                            color: AppColors.textPrimary,
                          ),
                ),
              ),
            ),
          ),
        ],
      ),
      body: Obx(
        () =>
            controller.isLoading
                ? const Center(child: CircularProgressIndicator())
                : controller.error.isNotEmpty
                ? _buildErrorState(controller)
                : _buildProfileContent(controller),
      ),
    );
  }

  Widget _buildErrorState(ProfileController controller) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: AppColors.error.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16),
            Text(
              controller.error,
              style: const TextStyle(
                fontSize: 16,
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            PrimaryButton(
              text: 'إعادة المحاولة',
              onPressed: controller.loadUserProfile,
              icon: Icons.refresh,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileContent(ProfileController controller) {
    return RefreshIndicator(
      onRefresh: controller.refreshUserProfile,
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Profile Header
            _buildProfileHeader(controller),

            const SizedBox(height: 24),

            // Personal Information
            _buildPersonalInformation(controller),

            const SizedBox(height: 16),

            // Role-specific Information
            if (controller.isEmployee) ...[
              _buildEmployeeInformation(controller),
              const SizedBox(height: 16),
            ] else if (controller.isMaster || controller.isManager) ...[
              _buildManagementInformation(controller),
              const SizedBox(height: 16),
            ],

            // Account Information
            _buildAccountInformation(controller),

            const SizedBox(height: 16),

            // Location Information (if available)
            if (controller.hasLocationData()) ...[
              _buildLocationInformation(controller),
              const SizedBox(height: 16),
            ],

            // Action Buttons
            _buildActionButtons(controller),

            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileHeader(ProfileController controller) {
    final user = controller.userProfile;
    if (user == null) return const SizedBox.shrink();

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [AppColors.primary, AppColors.primaryLight],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withValues(alpha: 0.3),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        children: [
          CircleAvatar(
            radius: 40,
            backgroundColor: Colors.white.withValues(alpha: 0.2),
            child: Text(
              user.firstName?.substring(0, 1).toUpperCase() ??
                  user.username.substring(0, 1).toUpperCase(),
              style: const TextStyle(
                fontSize: 32,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ),
          const SizedBox(height: 16),
          Text(
            user.fullName,
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              user.displayRole,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPersonalInformation(ProfileController controller) {
    final user = controller.userProfile;
    if (user == null) return const SizedBox.shrink();

    return InfoCard(
      title: 'المعلومات الشخصية',
      child: Column(
        children: [
          _buildInfoRow('الاسم الكامل', user.fullName),
          _buildInfoRow('اسم المستخدم', user.username),
          if (user.email != null && user.email!.isNotEmpty)
            _buildInfoRow('البريد الإلكتروني', user.email!),
          if (user.phone != null && user.phone!.isNotEmpty)
            _buildInfoRow('رقم الهاتف', user.phone!),
        ],
      ),
    );
  }

  Widget _buildEmployeeInformation(ProfileController controller) {
    final user = controller.userProfile;
    if (user == null) return const SizedBox.shrink();

    return InfoCard(
      title: 'معلومات الموظف',
      child: Column(
        children: [
          _buildInfoRow('المكتب', controller.getOfficeName()),
          if (controller.getOfficeAddress() != 'غير محدد')
            _buildInfoRow('عنوان المكتب', controller.getOfficeAddress()),
          if (user.commissionRate != null)
            _buildInfoRow(
              'معدل العمولة',
              controller.getCommissionRatePercentage(),
            ),
        ],
      ),
    );
  }

  Widget _buildManagementInformation(ProfileController controller) {
    final user = controller.userProfile;
    if (user == null) return const SizedBox.shrink();

    return InfoCard(
      title: controller.isMaster ? 'معلومات المدير العام' : 'معلومات المدير',
      child: Column(
        children: [
          _buildInfoRow('المكتب', controller.getOfficeName()),
          if (controller.getOfficeAddress() != 'غير محدد')
            _buildInfoRow('عنوان المكتب', controller.getOfficeAddress()),
          _buildInfoRow('الصلاحيات', _getPermissionsText(controller)),
        ],
      ),
    );
  }

  Widget _buildAccountInformation(ProfileController controller) {
    return InfoCard(
      title: 'معلومات الحساب',
      child: Column(
        children: [
          _buildInfoRow('تاريخ الانضمام', controller.getFormattedJoinDate()),
          _buildInfoRow(
            'نوع الحساب',
            controller.userProfile?.displayRole ?? 'غير محدد',
          ),
          if (controller.userProfile?.lastLocationUpdate != null)
            _buildInfoRow(
              'آخر تحديث للموقع',
              controller.getFormattedLastLocationUpdate(),
            ),
        ],
      ),
    );
  }

  Widget _buildLocationInformation(ProfileController controller) {
    return InfoCard(
      title: 'معلومات الموقع',
      child: Column(
        children: [
          _buildInfoRow('الإحداثيات', controller.getLocationCoordinates()),
          _buildInfoRow(
            'آخر تحديث',
            controller.getFormattedLastLocationUpdate(),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(ProfileController controller) {
    return Column(
      children: [
        PrimaryButton(
          text: 'تعديل الملف الشخصي',
          onPressed: controller.navigateToEditProfile,
          icon: Icons.edit,
          width: double.infinity,
        ),
        const SizedBox(height: 12),
        SecondaryButton(
          text: 'تغيير كلمة المرور',
          onPressed: controller.navigateToChangePassword,
          icon: Icons.lock,
          width: double.infinity,
        ),
        const SizedBox(height: 12),
        SecondaryButton(
          text: 'تسجيل الخروج',
          onPressed: controller.logout,
          icon: Icons.logout,
          width: double.infinity,
          textColor: AppColors.error,
          borderColor: AppColors.error,
        ),
      ],
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: AppColors.textSecondary,
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 14,
                color: AppColors.textPrimary,
              ),
              textAlign: TextAlign.end,
            ),
          ),
        ],
      ),
    );
  }

  String _getPermissionsText(ProfileController controller) {
    if (controller.isMaster) {
      return 'جميع الصلاحيات';
    } else if (controller.isManager) {
      return 'إدارة الموظفين والطلبات';
    }
    return 'غير محدد';
  }
}
