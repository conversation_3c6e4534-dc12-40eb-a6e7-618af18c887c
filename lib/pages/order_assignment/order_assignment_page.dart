import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:myrunway/pages/order_assignment/order_assignment_controller.dart';
import 'package:myrunway/core/models/order_model_new.dart';
import 'package:myrunway/core/models/user_model.dart';
import 'package:myrunway/core/constants/app_colors.dart';
import 'package:myrunway/widgets/autocomplete_text_field.dart';

class OrderAssignmentPage extends StatelessWidget {
  const OrderAssignmentPage({super.key});

  @override
  Widget build(BuildContext context) {
    final OrderAssignmentController controller = Get.put(
      OrderAssignmentController(),
    );

    return Scaffold(
      appBar: AppBar(
        title: const Text('تعيين الطلبات'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          Obx(
            () => IconButton(
              icon: Icon(
                controller.viewMode == ViewMode.individual
                    ? Icons.group_work
                    : Icons.list,
              ),
              onPressed: controller.toggleViewMode,
              tooltip:
                  controller.viewMode == ViewMode.individual
                      ? 'عرض مجمع حسب العنوان'
                      : 'عرض فردي',
            ),
          ),
        ],
      ),
      body: Obx(() {
        if (controller.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        return Column(
          children: [
            // Employee selection dropdown
            _EmployeeSelectionCard(controller: controller),

            // Search and filter section
            _SearchFilterSection(controller: controller),

            // View mode toggle and selection info
            _ViewModeHeader(controller: controller),

            // Orders list
            Expanded(
              child:
                  controller.viewMode == ViewMode.individual
                      ? _IndividualOrdersList(controller: controller)
                      : _GroupedOrdersList(controller: controller),
            ),
          ],
        );
      }),
    );
  }
}

class _EmployeeSelectionCard extends StatelessWidget {
  final OrderAssignmentController controller;

  const _EmployeeSelectionCard({required this.controller});

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'اختر الموظف المراد تعيين الطلبات إليه:',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            Obx(
              () => DropdownButtonFormField<UserModel>(
                value: controller.selectedEmployee,
                decoration: const InputDecoration(
                  border: OutlineInputBorder(),
                  hintText: 'اختر موظف...',
                ),
                items:
                    controller.employees
                        .map(
                          (employee) => DropdownMenuItem<UserModel>(
                            value: employee,
                            child: Text(employee.fullName),
                          ),
                        )
                        .toList(),
                onChanged: controller.selectEmployee,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _SearchFilterSection extends StatefulWidget {
  final OrderAssignmentController controller;

  const _SearchFilterSection({required this.controller});

  @override
  State<_SearchFilterSection> createState() => _SearchFilterSectionState();
}

class _SearchFilterSectionState extends State<_SearchFilterSection> {
  final TextEditingController _searchController = TextEditingController();
  bool _isExpanded = false;

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          ListTile(
            leading: const Icon(Icons.search),
            title: const Text('البحث والتصفية'),
            trailing: IconButton(
              icon: Icon(_isExpanded ? Icons.expand_less : Icons.expand_more),
              onPressed: () {
                setState(() {
                  _isExpanded = !_isExpanded;
                });
              },
            ),
            onTap: () {
              setState(() {
                _isExpanded = !_isExpanded;
              });
            },
          ),
          if (_isExpanded) ...[
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  // Customer search with autocomplete
                  AutocompleteTextField(
                    controller: _searchController,
                    type: AutocompleteType.customerData,
                    labelText: 'البحث بالعميل أو العنوان',
                    hintText: 'ابحث باسم العميل أو رقم الهاتف أو العنوان...',
                    prefixIcon: const Icon(Icons.search),
                    onChanged: (value) {
                      widget.controller.filterOrders(value);
                    },
                    onSuggestionSelected: (suggestion) {
                      widget.controller.filterOrders(suggestion.value);
                    },
                  ),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () {
                            _searchController.clear();
                            widget.controller.clearFilter();
                          },
                          icon: const Icon(Icons.clear),
                          label: const Text('مسح البحث'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.grey[300],
                            foregroundColor: Colors.black87,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }
}

class _ViewModeHeader extends StatelessWidget {
  final OrderAssignmentController controller;

  const _ViewModeHeader({required this.controller});

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              controller.viewMode == ViewMode.individual
                  ? 'عرض فردي (${controller.unassignedOrders.length} طلب)'
                  : 'عرض مجمع (${controller.groupedOrders.length} عنوان)',
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                if (controller.selectedOrderIds.isNotEmpty) ...[
                  Text(
                    '${controller.selectedOrderIds.length} محدد',
                    style: TextStyle(color: AppColors.primary),
                  ),
                  const SizedBox(width: 8),
                  TextButton(
                    onPressed: controller.clearSelections,
                    child: const Text('إلغاء التحديد'),
                  ),
                  const SizedBox(width: 8),
                  ElevatedButton(
                    onPressed:
                        controller.selectedEmployee != null
                            ? () => controller.showAssignmentDialog()
                            : null,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('تعيين المحدد'),
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class _IndividualOrdersList extends StatelessWidget {
  final OrderAssignmentController controller;

  const _IndividualOrdersList({required this.controller});

  @override
  Widget build(BuildContext context) {
    if (controller.unassignedOrders.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.assignment_turned_in, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'لا توجد طلبات غير مُعيَّنة',
              style: TextStyle(fontSize: 18, color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: controller.refreshData,
      child: Obx(
        () => ListView.builder(
          key: Key("unassignedOrders"),
          padding: const EdgeInsets.all(16),
          itemCount: controller.unassignedOrders.length,
          itemBuilder: (context, index) {
            final order = controller.unassignedOrders[index];
            return Obx(() {
              var contains = controller.selectedOrderIds.contains(order.id!);
              return _OrderCard(
                key: Key(order.id.toString() + contains.toString()),
                order: order,
                controller: controller,
                isSelected: contains,
              );
            });
          },
        ),
      ),
    );
  }
}

class _GroupedOrdersList extends StatelessWidget {
  final OrderAssignmentController controller;

  const _GroupedOrdersList({required this.controller});

  @override
  Widget build(BuildContext context) {
    if (controller.groupedOrders.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.assignment_turned_in, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'لا توجد طلبات غير مُعيَّنة',
              style: TextStyle(fontSize: 18, color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: controller.refreshData,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: controller.groupedOrders.length,
        itemBuilder: (context, index) {
          final group = controller.groupedOrders[index];
          return _AddressGroupCard(group: group, controller: controller);
        },
      ),
    );
  }
}

class _OrderCard extends StatelessWidget {
  final OrderModelNew order;
  final OrderAssignmentController controller;
  final bool isSelected;

  const _OrderCard({
    super.key,
    required this.order,
    required this.controller,
    required this.isSelected,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: isSelected ? 4 : 2,
      color: isSelected ? AppColors.primary.withValues(alpha: 0.1) : null,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side:
            isSelected
                ? BorderSide(color: AppColors.primary, width: 2)
                : BorderSide.none,
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () => controller.toggleOrderSelection(order.id!),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      'طلب #${order.code}',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  if (isSelected)
                    Icon(Icons.check_circle, color: AppColors.primary),
                  const SizedBox(width: 8),
                  IconButton(
                    icon: const Icon(Icons.assignment_ind),
                    onPressed:
                        controller.selectedEmployee != null
                            ? () => controller.showAssignmentDialog(
                              singleOrderId: order.id,
                            )
                            : null,
                    color: AppColors.primary,
                    tooltip: 'تعيين هذا الطلب',
                  ),
                ],
              ),
              const SizedBox(height: 8),

              // Customer info
              Row(
                children: [
                  Icon(Icons.person, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      order.customerName,
                      style: TextStyle(
                        color: Colors.grey[800],
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),

              // Phone
              Row(
                children: [
                  Icon(Icons.phone, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(
                    order.customerPhone,
                    style: TextStyle(color: Colors.grey[600], fontSize: 14),
                  ),
                ],
              ),

              // Address
              if (order.customerAddress != null) ...[
                const SizedBox(height: 4),
                Row(
                  children: [
                    Icon(Icons.location_on, size: 16, color: Colors.grey[600]),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        order.customerAddress!,
                        style: TextStyle(color: Colors.grey[600], fontSize: 14),
                      ),
                    ),
                  ],
                ),
              ],

              // Total price
              if (order.totalPrice != null) ...[
                const SizedBox(height: 8),
                Row(
                  children: [
                    Icon(
                      Icons.attach_money,
                      size: 16,
                      color: Colors.green[600],
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '${order.totalPrice!.toStringAsFixed(2)} جنيه',
                      style: TextStyle(
                        color: Colors.green[600],
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ],

              const SizedBox(height: 8),
              Text(
                'تاريخ الإنشاء: ${_formatDate(order.createdAt)}',
                style: TextStyle(color: Colors.grey[600], fontSize: 12),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}

class _AddressGroupCard extends StatelessWidget {
  final OrdersByAddress group;
  final OrderAssignmentController controller;

  const _AddressGroupCard({required this.group, required this.controller});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final selectedCount =
          group.orders
              .where((order) => controller.selectedOrderIds.contains(order.id))
              .length;
      final isAllSelected = selectedCount == group.orders.length;
      return Card(
        margin: const EdgeInsets.only(bottom: 12),
        elevation: 2,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: ExpansionTile(
          title: Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      group.address,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      '${group.orders.length} طلب',
                      style: TextStyle(color: Colors.grey[600], fontSize: 14),
                    ),
                  ],
                ),
              ),
              if (selectedCount > 0)
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.primary,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '$selectedCount محدد',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
            ],
          ),
          trailing: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              IconButton(
                icon: Icon(
                  isAllSelected
                      ? Icons.check_box
                      : Icons.check_box_outline_blank,
                  color: AppColors.primary,
                ),
                onPressed: () {
                  if (isAllSelected) {
                    // Deselect all orders in this group
                    for (final order in group.orders) {
                      controller.selectedOrderIds.remove(order.id);
                    }
                  } else {
                    // Select all orders in this group
                    controller.selectOrderGroup(group.orders);
                  }
                },
                tooltip: isAllSelected ? 'إلغاء تحديد الكل' : 'تحديد الكل',
              ),

              IconButton(
                icon: const Icon(Icons.assignment_ind),
                onPressed:
                    controller.selectedEmployee != null
                        ? () => controller.showAssignmentDialog(
                          multipleOrderIds:
                              group.orders.map((order) => order.id!).toList(),
                        )
                        : null,
                color: AppColors.primary,
                tooltip: 'تعيين جميع طلبات هذا العنوان',
              ),
            ],
          ),
          children:
              group.orders
                  .map(
                    (order) => Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: _OrderCard(
                        order: order,
                        controller: controller,
                        isSelected: controller.selectedOrderIds.contains(
                          order.id,
                        ),
                      ),
                    ),
                  )
                  .toList(),
        ),
      );
    });
  }
}
