import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:myrunway/core/models/order_model_new.dart';
import 'package:myrunway/core/models/user_model.dart';
import 'package:myrunway/core/services/auth_service.dart';
import 'package:myrunway/core/services/order_service.dart';
import 'package:myrunway/core/services/user_service.dart';
import 'package:myrunway/core/constants/user_roles.dart';

enum ViewMode { individual, grouped }

class OrdersByAddress {
  final String address;
  final List<OrderModelNew> orders;

  OrdersByAddress({required this.address, required this.orders});
}

class OrderAssignmentController extends GetxController {
  final OrderService _orderService = Get.find<OrderService>();
  final UserService _userService = Get.find<UserService>();
  final AuthService _authService = Get.find<AuthService>();

  // Reactive variables
  final RxList<OrderModelNew> _unassignedOrders = <OrderModelNew>[].obs;
  final RxList<OrderModelNew> _filteredOrders = <OrderModelNew>[].obs;
  final RxList<UserModel> _employees = <UserModel>[].obs;
  final RxBool _isLoading = false.obs;
  final RxBool _isAssigning = false.obs;
  final Rx<UserModel?> _selectedEmployee = Rx<UserModel?>(null);
  final Rx<ViewMode> _viewMode = ViewMode.individual.obs;
  final RxList<OrdersByAddress> _groupedOrders = <OrdersByAddress>[].obs;
  final RxList<int> _selectedOrderIds = <int>[].obs;
  final RxString _searchQuery = ''.obs;

  // Getters
  List<OrderModelNew> get unassignedOrders =>
      _searchQuery.value.isEmpty ? _unassignedOrders : _filteredOrders;
  List<UserModel> get employees => _employees;
  bool get isLoading => _isLoading.value;
  bool get isAssigning => _isAssigning.value;
  UserModel? get selectedEmployee => _selectedEmployee.value;
  ViewMode get viewMode => _viewMode.value;
  List<OrdersByAddress> get groupedOrders => _groupedOrders;
  List<int> get selectedOrderIds => _selectedOrderIds;
  String get searchQuery => _searchQuery.value;

  // Permission getters
  bool get canAccessOrderAssignment =>
      UserPermissions.canAccessOrderAssignment(_authService.currentUserRole!);

  @override
  void onInit() {
    super.onInit();
    if (canAccessOrderAssignment) {
      loadData();
    } else {
      Get.back();
      Get.snackbar(
        'خطأ في الصلاحية',
        'ليس لديك صلاحية للوصول إلى صفحة تعيين الطلبات',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  // Load initial data
  Future<void> loadData() async {
    _isLoading.value = true;
    await Future.wait([loadUnassignedOrders(), loadEmployees()]);
    _isLoading.value = false;
  }

  // Load unassigned orders
  Future<void> loadUnassignedOrders() async {
    final response = await _orderService.getUnassignedOrders();

    if (response.success && response.data != null) {
      _unassignedOrders.value = response.data!;
      _groupOrdersByAddress();
    } else {
      Get.snackbar(
        'خطأ',
        response.message ?? 'فشل في جلب الطلبات غير المُعيَّنة',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  // Load employees
  Future<void> loadEmployees() async {
    final response = await _userService.getEmployees();

    if (response.success && response.data != null) {
      _employees.value = response.data!;
    } else {
      Get.snackbar(
        'خطأ',
        response.message ?? 'فشل في جلب قائمة الموظفين',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  // Group orders by delivery address using fuzzy matching
  void _groupOrdersByAddress() {
    final Map<String, List<OrderModelNew>> grouped = {};
    final List<String> processedAddresses = [];

    for (final order in _unassignedOrders) {
      final address = order.customerAddress ?? 'عنوان غير محدد';

      // Find if there's a similar address already processed
      String? matchingAddress = _findSimilarAddress(
        address,
        processedAddresses,
      );

      if (matchingAddress != null) {
        // Add to existing group
        grouped[matchingAddress]!.add(order);
      } else {
        // Create new group
        grouped[address] = [order];
        processedAddresses.add(address);
      }
    }

    _groupedOrders.value =
        grouped.entries
            .map(
              (entry) =>
                  OrdersByAddress(address: entry.key, orders: entry.value),
            )
            .toList();
  }

  // Find similar address using fuzzy matching
  String? _findSimilarAddress(String address, List<String> existingAddresses) {
    const double similarityThreshold = 0.75;

    for (final existingAddress in existingAddresses) {
      final similarity = _calculateAddressSimilarity(address, existingAddress);
      if (similarity >= similarityThreshold) {
        return existingAddress;
      }
    }
    return null;
  }

  // Calculate similarity between two Arabic addresses
  double _calculateAddressSimilarity(String address1, String address2) {
    // Normalize both addresses for comparison
    final normalized1 = _normalizeArabicAddress(address1);
    final normalized2 = _normalizeArabicAddress(address2);

    // If normalized addresses are identical, return perfect match
    if (normalized1 == normalized2) return 1.0;

    // Calculate Jaro-Winkler similarity for the normalized addresses
    final jaroWinklerSimilarity = _jaroWinklerSimilarity(
      normalized1,
      normalized2,
    );

    // Calculate token-based similarity (for handling word order differences)
    final tokenSimilarity = _calculateTokenSimilarity(normalized1, normalized2);

    // Combine both similarities with weights
    // Jaro-Winkler is better for character-level differences
    // Token similarity is better for word order differences
    return (jaroWinklerSimilarity * 0.7) + (tokenSimilarity * 0.3);
  }

  // Toggle view mode
  void toggleViewMode() {
    _viewMode.value =
        _viewMode.value == ViewMode.individual
            ? ViewMode.grouped
            : ViewMode.individual;
    _selectedOrderIds.clear();
  }

  // Select employee
  void selectEmployee(UserModel? employee) {
    _selectedEmployee.value = employee;
  }

  // Toggle order selection
  void toggleOrderSelection(int orderId) {
    if (_selectedOrderIds.contains(orderId)) {
      _selectedOrderIds.remove(orderId);
    } else {
      _selectedOrderIds.add(orderId);
    }
  }

  // Select all orders in a group
  void selectOrderGroup(List<OrderModelNew> orders) {
    final orderIds = orders.map((order) => order.id!).toList();
    for (final orderId in orderIds) {
      if (!_selectedOrderIds.contains(orderId)) {
        _selectedOrderIds.add(orderId);
      }
    }
  }

  // Clear all selections
  void clearSelections() {
    _selectedOrderIds.clear();
  }

  // Show assignment dialog
  void showAssignmentDialog({int? singleOrderId, List<int>? multipleOrderIds}) {
    if (_selectedEmployee.value == null) {
      Get.snackbar(
        'خطأ',
        'يرجى اختيار موظف أولاً',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    final TextEditingController commissionController = TextEditingController();

    Get.dialog(
      AlertDialog(
        title: const Text('تعيين الطلب'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'تعيين ${singleOrderId != null ? 'طلب واحد' : '${multipleOrderIds?.length ?? _selectedOrderIds.length} طلب'} إلى ${_selectedEmployee.value!.fullName}',
            ),
            const SizedBox(height: 16),
            TextField(
              controller: commissionController,
              decoration: const InputDecoration(
                labelText: 'معدل العمولة الخاص (اختياري)',
                hintText: 'اتركه فارغاً لاستخدام المعدل الافتراضي',
                suffixText: '%',
              ),
              keyboardType: TextInputType.number,
            ),
          ],
        ),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text('إلغاء')),
          Obx(
            () => ElevatedButton(
              onPressed:
                  _isAssigning.value
                      ? null
                      : () => _performAssignment(
                        commissionController.text.trim(),
                        singleOrderId: singleOrderId,
                        multipleOrderIds: multipleOrderIds,
                      ),
              child:
                  _isAssigning.value
                      ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                      : const Text('تعيين'),
            ),
          ),
        ],
      ),
    );
  }

  // Perform assignment
  Future<void> _performAssignment(
    String commissionText, {
    int? singleOrderId,
    List<int>? multipleOrderIds,
  }) async {
    _isAssigning.value = true;

    final double? commissionRate =
        commissionText.isNotEmpty ? double.tryParse(commissionText) : null;

    final request = OrderAssignRequest(
      employeeId: _selectedEmployee.value!.id!,
      specialCommissionRate: commissionRate,
    );

    try {
      if (singleOrderId != null) {
        // Single order assignment
        final response = await _orderService.assignOrder(
          singleOrderId,
          request,
        );
        if (response.success) {
          Get.back(); // Close dialog
          Get.snackbar(
            'نجح',
            'تم تعيين الطلب بنجاح',
            backgroundColor: Colors.green,
            colorText: Colors.white,
          );
          await loadUnassignedOrders(); // Refresh data
        } else {
          Get.snackbar(
            'خطأ',
            response.message ?? 'فشل في تعيين الطلب',
            backgroundColor: Colors.red,
            colorText: Colors.white,
          );
        }
      } else {
        // Multiple orders assignment
        final orderIds = multipleOrderIds ?? _selectedOrderIds.toList();
        final response = await _orderService.assignMultipleOrders(
          orderIds,
          request,
        );

        Get.back(); // Close dialog

        if (response.success) {
          Get.snackbar(
            'نجح',
            response.message ?? 'تم تعيين الطلبات بنجاح',
            backgroundColor: Colors.green,
            colorText: Colors.white,
          );
          _selectedOrderIds.clear();
          await loadUnassignedOrders(); // Refresh data
        } else {
          Get.snackbar(
            'خطأ',
            response.message ?? 'فشل في تعيين الطلبات',
            backgroundColor: Colors.red,
            colorText: Colors.white,
          );
        }
      }
    } finally {
      _isAssigning.value = false;
    }
  }

  // Refresh data
  Future<void> refreshData() async {
    await loadData();
  }

  // Filter orders based on search query
  void filterOrders(String query) {
    _searchQuery.value = query.trim();

    if (_searchQuery.value.isEmpty) {
      _filteredOrders.clear();
      _groupOrdersByAddress();
      return;
    }

    final filtered =
        _unassignedOrders.where((order) {
          final customerName = order.customerName.toLowerCase();
          final customerPhone = order.customerPhone;
          final customerAddress = order.customerAddress?.toLowerCase() ?? '';
          final orderCode = order.code.toLowerCase();
          final searchLower = _searchQuery.value.toLowerCase();

          // Check exact matches first
          if (customerName.contains(searchLower) ||
              customerPhone.contains(_searchQuery.value) ||
              orderCode.contains(searchLower)) {
            return true;
          }

          // Check fuzzy match for address
          if (customerAddress.isNotEmpty) {
            final similarity = _calculateAddressSimilarity(
              customerAddress,
              _searchQuery.value,
            );
            if (similarity >= 0.5) {
              return true;
            }
          }

          return false;
        }).toList();

    _filteredOrders.value = filtered;
    _groupFilteredOrdersByAddress();
  }

  // Clear search filter
  void clearFilter() {
    _searchQuery.value = '';
    _filteredOrders.clear();
    _groupOrdersByAddress();
  }

  // Group filtered orders by address
  void _groupFilteredOrdersByAddress() {
    final Map<String, List<OrderModelNew>> grouped = {};
    final List<String> processedAddresses = [];

    for (final order in _filteredOrders) {
      final address = order.customerAddress ?? 'عنوان غير محدد';

      // Find if there's a similar address already processed
      String? matchingAddress = _findSimilarAddress(
        address,
        processedAddresses,
      );

      if (matchingAddress != null) {
        // Add to existing group
        grouped[matchingAddress]!.add(order);
      } else {
        // Create new group
        grouped[address] = [order];
        processedAddresses.add(address);
      }
    }

    _groupedOrders.value =
        grouped.entries
            .map(
              (entry) =>
                  OrdersByAddress(address: entry.key, orders: entry.value),
            )
            .toList();
  }

  // Normalize Arabic address for better matching
  // Examples of addresses that will be grouped together:
  // "شارع الملك فهد" and "ش الملك فهد" -> both become "ش ملك فهد"
  // "حي النزهة، الرياض" and "حي النزهه - الرياض" -> both become "ح نزهه رياض"
  // "طريق الملك عبدالعزيز" and "ط الملك عبد العزيز" -> similar after normalization
  String _normalizeArabicAddress(String address) {
    String normalized = address.toLowerCase().trim();

    // Remove extra whitespace and normalize spaces
    normalized = normalized.replaceAll(RegExp(r'\s+'), ' ');

    // Remove common punctuation
    normalized = normalized.replaceAll(RegExp(r'[،,.\-_()[\]{}]'), '');

    // Normalize Arabic street abbreviations
    final Map<String, String> streetAbbreviations = {
      'شارع': 'ش',
      'طريق': 'ط',
      'حي': 'ح',
      'منطقة': 'م',
      'مدينة': 'مد',
      'قرية': 'ق',
      'مجمع': 'مج',
      'مركز': 'مر',
      'شمال': 'ش',
      'جنوب': 'ج',
      'شرق': 'شر',
      'غرب': 'غ',
      'الملك': 'ملك',
      'الأمير': 'أمير',
      'الإمام': 'إمام',
    };

    // Replace full forms with abbreviations for consistency
    streetAbbreviations.forEach((full, abbrev) {
      normalized = normalized.replaceAll(full, abbrev);
    });

    // Handle common Arabic character variations
    normalized = normalized.replaceAll('أ', 'ا');
    normalized = normalized.replaceAll('إ', 'ا');
    normalized = normalized.replaceAll('آ', 'ا');
    normalized = normalized.replaceAll('ة', 'ه');
    normalized = normalized.replaceAll('ى', 'ي');

    // Remove definite article "ال" for better matching
    normalized = normalized.replaceAll(RegExp(r'\bال'), '');

    return normalized.trim();
  }

  // Calculate token-based similarity (Jaccard similarity)
  double _calculateTokenSimilarity(String address1, String address2) {
    final tokens1 =
        address1.split(' ').where((token) => token.isNotEmpty).toSet();
    final tokens2 =
        address2.split(' ').where((token) => token.isNotEmpty).toSet();

    if (tokens1.isEmpty && tokens2.isEmpty) return 1.0;
    if (tokens1.isEmpty || tokens2.isEmpty) return 0.0;

    final intersection = tokens1.intersection(tokens2);
    final union = tokens1.union(tokens2);

    return intersection.length / union.length;
  }

  // Jaro-Winkler similarity implementation
  double _jaroWinklerSimilarity(String s1, String s2) {
    if (s1 == s2) return 1.0;
    if (s1.isEmpty || s2.isEmpty) return 0.0;

    final jaroSimilarity = _jaroSimilarity(s1, s2);

    if (jaroSimilarity < 0.7) return jaroSimilarity;

    // Calculate common prefix length (up to 4 characters)
    int prefixLength = 0;
    final maxPrefix = 4;
    final minLength = s1.length < s2.length ? s1.length : s2.length;
    final maxPrefixLength = minLength < maxPrefix ? minLength : maxPrefix;

    for (int i = 0; i < maxPrefixLength; i++) {
      if (s1[i] == s2[i]) {
        prefixLength++;
      } else {
        break;
      }
    }

    return jaroSimilarity + (0.1 * prefixLength * (1 - jaroSimilarity));
  }

  // Jaro similarity implementation
  double _jaroSimilarity(String s1, String s2) {
    final len1 = s1.length;
    final len2 = s2.length;

    if (len1 == 0 && len2 == 0) return 1.0;
    if (len1 == 0 || len2 == 0) return 0.0;

    final matchWindow = ((len1 > len2 ? len1 : len2) / 2 - 1).floor();
    if (matchWindow < 0) return 0.0;

    final s1Matches = List<bool>.filled(len1, false);
    final s2Matches = List<bool>.filled(len2, false);

    int matches = 0;
    int transpositions = 0;

    // Find matches
    for (int i = 0; i < len1; i++) {
      final start = i - matchWindow > 0 ? i - matchWindow : 0;
      final end = i + matchWindow + 1 < len2 ? i + matchWindow + 1 : len2;

      for (int j = start; j < end; j++) {
        if (s2Matches[j] || s1[i] != s2[j]) continue;
        s1Matches[i] = true;
        s2Matches[j] = true;
        matches++;
        break;
      }
    }

    if (matches == 0) return 0.0;

    // Find transpositions
    int k = 0;
    for (int i = 0; i < len1; i++) {
      if (!s1Matches[i]) continue;
      while (!s2Matches[k]) {
        k++;
      }
      if (s1[i] != s2[k]) transpositions++;
      k++;
    }

    return (matches / len1 +
            matches / len2 +
            (matches - transpositions / 2) / matches) /
        3;
  }
}
