import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:myrunway/pages/company_channels/company_channel_edit_controller.dart';
import 'package:myrunway/core/constants/app_colors.dart';
import 'package:myrunway/core/models/company_channel_model.dart';
import 'package:myrunway/core/models/company_model.dart';

class CompanyChannelEditPage extends StatelessWidget {
  final CompanyChannelModel channel;

  const CompanyChannelEditPage({super.key, required this.channel});

  @override
  Widget build(BuildContext context) {
    final CompanyChannelEditController controller = Get.put(
      CompanyChannelEditController(channel: channel),
    );

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('تعديل قناة الشركة'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          TextButton(
            onPressed: controller.resetForm,
            child: const Text('إعادة تعيين'),
          ),
        ],
      ),
      body: Obx(() {
        if (!controller.canEditChannels) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.lock, size: 64, color: Colors.grey),
                SizedBox(height: 16),
                Text(
                  'ليس لديك صلاحية لتعديل قنوات الشركات',
                  style: TextStyle(fontSize: 16),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Form(
            key: controller.formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Current Channel Info Card
                Card(
                  elevation: 2,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(Icons.info, color: AppColors.primary),
                            const SizedBox(width: 8),
                            const Text(
                              'معلومات القناة الحالية',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),
                        Text('الاسم: ${channel.name}'),
                        Text('الشركة: ${channel.company.name}'),
                        Text('الواتساب: ${channel.channelWhatsappNumber}'),
                        if (channel.notes != null && channel.notes!.isNotEmpty)
                          Text('الملاحظات: ${channel.notes!}'),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 16),

                // Edit Form Card
                Card(
                  elevation: 2,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Header
                        Row(
                          children: [
                            Icon(Icons.edit, color: AppColors.primary),
                            const SizedBox(width: 8),
                            const Text(
                              'تعديل المعلومات',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 24),

                        // Company Selection
                        const Text(
                          'الشركة *',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Obx(() => DropdownButtonFormField<CompanyModel>(
                          value: controller.selectedCompany,
                          decoration: InputDecoration(
                            hintText: 'اختر الشركة',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            contentPadding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 12,
                            ),
                          ),
                          items: controller.companies.map((company) {
                            return DropdownMenuItem(
                              value: company,
                              child: Text(company.name),
                            );
                          }).toList(),
                          onChanged: controller.setSelectedCompany,
                          validator: (value) {
                            if (value == null) {
                              return 'يجب اختيار شركة';
                            }
                            return null;
                          },
                          isExpanded: true,
                        )),
                        const SizedBox(height: 16),

                        // Channel Name
                        const Text(
                          'اسم القناة *',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: 8),
                        TextFormField(
                          controller: controller.nameController,
                          decoration: InputDecoration(
                            hintText: 'أدخل اسم القناة',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            contentPadding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 12,
                            ),
                          ),
                          validator: controller.validateName,
                          textInputAction: TextInputAction.next,
                        ),
                        const SizedBox(height: 16),

                        // WhatsApp Number
                        const Text(
                          'رقم الواتساب *',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: 8),
                        TextFormField(
                          controller: controller.whatsappNumberController,
                          decoration: InputDecoration(
                            hintText: 'أدخل رقم الواتساب',
                            prefixText: '+',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            contentPadding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 12,
                            ),
                          ),
                          keyboardType: TextInputType.phone,
                          validator: controller.validateWhatsAppNumber,
                          textInputAction: TextInputAction.next,
                        ),
                        const SizedBox(height: 16),

                        // Notes
                        const Text(
                          'ملاحظات (اختياري)',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: 8),
                        TextFormField(
                          controller: controller.notesController,
                          decoration: InputDecoration(
                            hintText: 'أدخل ملاحظات إضافية',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            contentPadding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 12,
                            ),
                          ),
                          maxLines: 3,
                          textInputAction: TextInputAction.done,
                        ),
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 24),

                // Update Button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: controller.isUpdating
                        ? null
                        : controller.updateCompanyChannel,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: controller.isUpdating
                        ? const SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                Colors.white,
                              ),
                            ),
                          )
                        : const Text(
                            'حفظ التغييرات',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                  ),
                ),
              ],
            ),
          ),
        );
      }),
    );
  }
}
