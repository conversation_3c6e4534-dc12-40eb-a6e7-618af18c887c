import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:myrunway/core/models/company_channel_model.dart';
import 'package:myrunway/core/models/company_model.dart';
import 'package:myrunway/core/services/auth_service.dart';
import 'package:myrunway/core/services/company_channel_service.dart';
import 'package:myrunway/core/services/company_service.dart';
import 'package:myrunway/core/constants/user_roles.dart';

class CompanyChannelsListController extends GetxController {
  final CompanyChannelService _companyChannelService =
      Get.find<CompanyChannelService>();
  final CompanyService _companyService = Get.find<CompanyService>();
  final AuthService _authService = Get.find<AuthService>();

  // Reactive variables
  final RxList<CompanyChannelModel> _companyChannels =
      <CompanyChannelModel>[].obs;
  final RxList<CompanyChannelModel> _filteredChannels =
      <CompanyChannelModel>[].obs;
  final RxList<CompanyModel> _companies = <CompanyModel>[].obs;
  final RxBool _isLoading = false.obs;
  final RxBool _isLoadingCompanies = false.obs;
  final RxString _error = ''.obs;
  final Rx<CompanyModel?> _selectedCompany = Rx<CompanyModel?>(null);
  final RxString _searchQuery = ''.obs;
  final RxList<int> _selectedChannelIds = <int>[].obs;
  final RxBool _isSelectionMode = false.obs;
  final RxBool _isBulkDeleting = false.obs;
  final RxString _sortBy = 'name'.obs; // name, company, date, whatsapp
  final RxBool _sortAscending = true.obs;

  // Search controller
  final TextEditingController searchController = TextEditingController();

  // Getters
  List<CompanyChannelModel> get companyChannels => _companyChannels;
  List<CompanyChannelModel> get filteredChannels => _filteredChannels;
  List<CompanyModel> get companies => _companies;
  bool get isLoading => _isLoading.value;
  bool get isLoadingCompanies => _isLoadingCompanies.value;
  String get error => _error.value;
  bool get hasError => _error.value.isNotEmpty;
  CompanyModel? get selectedCompany => _selectedCompany.value;
  String get searchQuery => _searchQuery.value;
  List<int> get selectedChannelIds => _selectedChannelIds;
  bool get isSelectionMode => _isSelectionMode.value;
  bool get isBulkDeleting => _isBulkDeleting.value;
  bool get hasSelectedChannels => _selectedChannelIds.isNotEmpty;
  String get sortBy => _sortBy.value;
  bool get sortAscending => _sortAscending.value;

  // Permission getters
  bool get canManageCompanyChannels =>
      _authService.hasPermission(UserRole.master);
  bool get canCreateChannels => _authService.hasPermission(UserRole.master);
  bool get canEditChannels => _authService.hasPermission(UserRole.master);
  bool get canDeleteChannels => _authService.hasPermission(UserRole.master);

  @override
  void onInit() {
    super.onInit();
    if (canManageCompanyChannels) {
      loadCompanies();
      loadCompanyChannels();
    }
  }

  @override
  void onClose() {
    searchController.dispose();
    super.onClose();
  }

  // Load all companies for filtering
  Future<void> loadCompanies() async {
    _isLoadingCompanies.value = true;
    _error.value = '';

    final response = await _companyService.getCompanies();

    if (response.success && response.data != null) {
      _companies.value = response.data!;
    } else {
      _error.value = response.message ?? 'فشل في جلب قائمة الشركات';
    }

    _isLoadingCompanies.value = false;
  }

  // Load company channels with optional company filter
  Future<void> loadCompanyChannels({int? companyId}) async {
    _isLoading.value = true;
    _error.value = '';

    final response = await _companyChannelService.getCompanyChannels(
      companyId: companyId ?? _selectedCompany.value?.id,
    );

    if (response.success && response.data != null) {
      _companyChannels.value = response.data!;
      _applyFilters();
    } else {
      _error.value = response.message ?? 'فشل في جلب قائمة قنوات الشركات';
      _companyChannels.clear();
      _filteredChannels.clear();
    }

    _isLoading.value = false;
  }

  // Filter channels by company
  void filterByCompany(CompanyModel? company) {
    _selectedCompany.value = company;
    _applyFilters();
  }

  // Clear company filter
  void clearFilter() {
    _selectedCompany.value = null;
    _applyFilters();
  }

  // Refresh data
  Future<void> refreshData() async {
    await Future.wait([loadCompanies(), loadCompanyChannels()]);
  }

  // Delete a company channel
  Future<void> deleteCompanyChannel(CompanyChannelModel channel) async {
    if (!canDeleteChannels) {
      Get.snackbar(
        'خطأ في الصلاحية',
        'ليس لديك صلاحية لحذف قنوات الشركات',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    // Show confirmation dialog
    final confirmed = await Get.dialog<bool>(
      AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف القناة "${channel.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Get.back(result: true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    // Perform deletion
    _isLoading.value = true;
    final response = await _companyChannelService.deleteCompanyChannel(
      channel.id!,
    );

    if (response.success) {
      Get.snackbar(
        'تم الحذف',
        'تم حذف القناة بنجاح',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
      // Refresh the list
      await loadCompanyChannels();
    } else {
      Get.snackbar(
        'خطأ في الحذف',
        response.message ?? 'فشل في حذف القناة',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }

    _isLoading.value = false;
  }

  // Update search query and filter channels
  void updateSearchQuery(String query) {
    _searchQuery.value = query;
    _applyFilters();
  }

  // Apply filters and search to channels
  void _applyFilters() {
    List<CompanyChannelModel> filtered = _companyChannels;

    // Apply company filter
    if (_selectedCompany.value != null) {
      filtered =
          filtered
              .where(
                (channel) => channel.company.id == _selectedCompany.value!.id,
              )
              .toList();
    }

    // Apply search filter
    if (_searchQuery.value.trim().isNotEmpty) {
      final lowerQuery = _searchQuery.value.toLowerCase().trim();
      filtered =
          filtered.where((channel) {
            return channel.name.toLowerCase().contains(lowerQuery) ||
                channel.channelWhatsappNumber.contains(lowerQuery) ||
                channel.company.name.toLowerCase().contains(lowerQuery);
          }).toList();
    }

    // Apply sorting
    filtered = _sortChannels(filtered);

    _filteredChannels.value = filtered;
  }

  // Sort channels based on current sort criteria
  List<CompanyChannelModel> _sortChannels(List<CompanyChannelModel> channels) {
    final sortedChannels = List<CompanyChannelModel>.from(channels);

    switch (_sortBy.value) {
      case 'name':
        sortedChannels.sort(
          (a, b) =>
              _sortAscending.value
                  ? a.name.toLowerCase().compareTo(b.name.toLowerCase())
                  : b.name.toLowerCase().compareTo(a.name.toLowerCase()),
        );
        break;
      case 'company':
        sortedChannels.sort(
          (a, b) =>
              _sortAscending.value
                  ? a.company.name.toLowerCase().compareTo(
                    b.company.name.toLowerCase(),
                  )
                  : b.company.name.toLowerCase().compareTo(
                    a.company.name.toLowerCase(),
                  ),
        );
        break;
      case 'date':
        sortedChannels.sort(
          (a, b) =>
              _sortAscending.value
                  ? a.createdAt.compareTo(b.createdAt)
                  : b.createdAt.compareTo(a.createdAt),
        );
        break;
      case 'whatsapp':
        sortedChannels.sort(
          (a, b) =>
              _sortAscending.value
                  ? a.channelWhatsappNumber.compareTo(b.channelWhatsappNumber)
                  : b.channelWhatsappNumber.compareTo(a.channelWhatsappNumber),
        );
        break;
    }

    return sortedChannels;
  }

  // Clear search
  void clearSearch() {
    searchController.clear();
    _searchQuery.value = '';
    _applyFilters();
  }

  // Sorting Methods

  // Change sort criteria
  void setSortBy(String sortBy) {
    if (_sortBy.value == sortBy) {
      // Toggle sort direction if same field
      _sortAscending.value = !_sortAscending.value;
    } else {
      // Set new sort field and default to ascending
      _sortBy.value = sortBy;
      _sortAscending.value = true;
    }
    _applyFilters();
  }

  // Get sort icon for a field
  IconData getSortIcon(String field) {
    if (_sortBy.value != field) {
      return Icons.sort;
    }
    return _sortAscending.value ? Icons.arrow_upward : Icons.arrow_downward;
  }

  // Get sort display name
  String getSortDisplayName(String sortBy) {
    switch (sortBy) {
      case 'name':
        return 'الاسم';
      case 'company':
        return 'الشركة';
      case 'date':
        return 'تاريخ الإنشاء';
      case 'whatsapp':
        return 'رقم الواتساب';
      default:
        return sortBy;
    }
  }

  // Bulk Operations Methods

  // Toggle selection mode
  void toggleSelectionMode() {
    _isSelectionMode.value = !_isSelectionMode.value;
    if (!_isSelectionMode.value) {
      _selectedChannelIds.clear();
    }
  }

  // Toggle channel selection
  void toggleChannelSelection(int channelId) {
    if (_selectedChannelIds.contains(channelId)) {
      _selectedChannelIds.remove(channelId);
    } else {
      _selectedChannelIds.add(channelId);
    }
  }

  // Check if channel is selected
  bool isChannelSelected(int channelId) {
    return _selectedChannelIds.contains(channelId);
  }

  // Select all visible channels
  void selectAllChannels() {
    _selectedChannelIds.clear();
    for (final channel in _filteredChannels) {
      if (channel.id != null) {
        _selectedChannelIds.add(channel.id!);
      }
    }
  }

  // Clear all selections
  void clearAllSelections() {
    _selectedChannelIds.clear();
  }

  // Bulk delete selected channels
  Future<void> bulkDeleteSelectedChannels() async {
    if (!canDeleteChannels) {
      Get.snackbar(
        'خطأ في الصلاحية',
        'ليس لديك صلاحية لحذف قنوات الشركات',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    if (_selectedChannelIds.isEmpty) {
      Get.snackbar(
        'لا توجد عناصر محددة',
        'يجب تحديد قناة واحدة على الأقل للحذف',
        backgroundColor: Colors.orange,
        colorText: Colors.white,
      );
      return;
    }

    // Show confirmation dialog
    final confirmed = await Get.dialog<bool>(
      AlertDialog(
        title: const Text('تأكيد الحذف المتعدد'),
        content: Text(
          'هل أنت متأكد من حذف ${_selectedChannelIds.length} قناة؟\n'
          'هذا الإجراء لا يمكن التراجع عنه.',
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Get.back(result: true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف الكل'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    _isBulkDeleting.value = true;

    int successCount = 0;
    int failureCount = 0;

    // Delete channels one by one
    for (final channelId in List<int>.from(_selectedChannelIds)) {
      final response = await _companyChannelService.deleteCompanyChannel(
        channelId,
      );
      if (response.success) {
        successCount++;
      } else {
        failureCount++;
      }
    }

    // Show result message
    if (successCount > 0 && failureCount == 0) {
      Get.snackbar(
        'تم الحذف بنجاح',
        'تم حذف $successCount قناة بنجاح',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } else if (successCount > 0 && failureCount > 0) {
      Get.snackbar(
        'حذف جزئي',
        'تم حذف $successCount قناة، فشل في حذف $failureCount قناة',
        backgroundColor: Colors.orange,
        colorText: Colors.white,
      );
    } else {
      Get.snackbar(
        'فشل في الحذف',
        'فشل في حذف جميع القنوات المحددة',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }

    // Clear selections and refresh data
    _selectedChannelIds.clear();
    _isSelectionMode.value = false;
    _isBulkDeleting.value = false;

    // Refresh the list
    await loadCompanyChannels();
  }

  // Export Methods

  // Export channels to CSV format (copy to clipboard)
  Future<void> exportChannelsToClipboard() async {
    if (_filteredChannels.isEmpty) {
      Get.snackbar(
        'لا توجد بيانات',
        'لا توجد قنوات للتصدير',
        backgroundColor: Colors.orange,
        colorText: Colors.white,
      );
      return;
    }

    final csvData = _generateCSVData(_filteredChannels);

    await Clipboard.setData(ClipboardData(text: csvData));

    Get.snackbar(
      'تم التصدير',
      'تم نسخ بيانات ${_filteredChannels.length} قناة إلى الحافظة',
      backgroundColor: Colors.green,
      colorText: Colors.white,
    );
  }

  // Generate CSV data from channels
  String _generateCSVData(List<CompanyChannelModel> channels) {
    final buffer = StringBuffer();

    // CSV Header
    buffer.writeln('اسم القناة,الشركة,رقم الواتساب,الملاحظات,تاريخ الإنشاء');

    // CSV Data
    for (final channel in channels) {
      final name = _escapeCsvField(channel.name);
      final company = _escapeCsvField(channel.company.name);
      final whatsapp = _escapeCsvField(channel.channelWhatsappNumber);
      final notes = _escapeCsvField(channel.notes ?? '');
      final createdAt = _formatDateForExport(channel.createdAt);

      buffer.writeln('$name,$company,$whatsapp,$notes,$createdAt');
    }

    return buffer.toString();
  }

  // Escape CSV field (handle commas and quotes)
  String _escapeCsvField(String field) {
    if (field.contains(',') || field.contains('"') || field.contains('\n')) {
      return '"${field.replaceAll('"', '""')}"';
    }
    return field;
  }

  // Format date for export
  String _formatDateForExport(DateTime date) {
    return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }

  // Export summary statistics
  Future<void> exportSummaryToClipboard() async {
    final summary = _generateSummaryData();

    await Clipboard.setData(ClipboardData(text: summary));

    Get.snackbar(
      'تم التصدير',
      'تم نسخ ملخص الإحصائيات إلى الحافظة',
      backgroundColor: Colors.green,
      colorText: Colors.white,
    );
  }

  // Generate summary statistics
  String _generateSummaryData() {
    final buffer = StringBuffer();

    buffer.writeln('=== ملخص قنوات الشركات ===');
    buffer.writeln('');
    buffer.writeln('إجمالي القنوات: ${_companyChannels.length}');
    buffer.writeln('القنوات المعروضة: ${_filteredChannels.length}');
    buffer.writeln('');

    // Group by company
    final companyCounts = <String, int>{};
    for (final channel in _companyChannels) {
      companyCounts[channel.company.name] =
          (companyCounts[channel.company.name] ?? 0) + 1;
    }

    buffer.writeln('=== توزيع القنوات حسب الشركة ===');
    for (final entry in companyCounts.entries) {
      buffer.writeln('${entry.key}: ${entry.value} قناة');
    }

    buffer.writeln('');
    buffer.writeln('تاريخ التصدير: ${_formatDateForExport(DateTime.now())}');

    return buffer.toString();
  }
}
