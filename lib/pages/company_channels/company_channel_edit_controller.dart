import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:myrunway/core/models/company_channel_model.dart';
import 'package:myrunway/core/models/company_model.dart';
import 'package:myrunway/core/services/auth_service.dart';
import 'package:myrunway/core/services/company_channel_service.dart';
import 'package:myrunway/core/services/company_service.dart';
import 'package:myrunway/core/constants/user_roles.dart';

class CompanyChannelEditController extends GetxController {
  final CompanyChannelService _companyChannelService =
      Get.find<CompanyChannelService>();
  final CompanyService _companyService = Get.find<CompanyService>();
  final AuthService _authService = Get.find<AuthService>();

  // Constructor parameter
  final CompanyChannelModel channel;

  // Form controllers
  final nameController = TextEditingController();
  final notesController = TextEditingController();
  final whatsappNumberController = TextEditingController();

  // Form key
  final formKey = GlobalKey<FormState>();

  // Reactive variables
  final RxBool _isUpdating = false.obs;
  final RxList<CompanyModel> _companies = <CompanyModel>[].obs;
  final RxBool _isLoadingCompanies = false.obs;
  final Rx<CompanyModel?> _selectedCompany = Rx<CompanyModel?>(null);

  CompanyChannelEditController({required this.channel});

  // Getters
  bool get isUpdating => _isUpdating.value;
  List<CompanyModel> get companies => _companies;
  bool get isLoadingCompanies => _isLoadingCompanies.value;
  CompanyModel? get selectedCompany => _selectedCompany.value;

  // Permission getters
  bool get canEditChannels => _authService.hasPermission(UserRole.master);

  @override
  void onInit() {
    super.onInit();
    if (canEditChannels) {
      _initializeForm();
      loadCompanies();
    }
  }

  @override
  void onClose() {
    nameController.dispose();
    notesController.dispose();
    whatsappNumberController.dispose();
    super.onClose();
  }

  // Initialize form with existing data
  void _initializeForm() {
    nameController.text = channel.name;
    notesController.text = channel.notes ?? '';
    whatsappNumberController.text = channel.channelWhatsappNumber;
    _selectedCompany.value = channel.company;
  }

  // Load companies for selection
  Future<void> loadCompanies() async {
    _isLoadingCompanies.value = true;

    final response = await _companyService.getCompanies();

    if (response.success && response.data != null) {
      _companies.value = response.data!;

      // Ensure current company is in the list
      if (_selectedCompany.value != null) {
        final currentCompany = _companies.firstWhereOrNull(
          (company) => company.id == _selectedCompany.value!.id,
        );
        if (currentCompany != null) {
          _selectedCompany.value = currentCompany;
        }
      }
    } else {
      Get.snackbar(
        'خطأ',
        response.message ?? 'فشل في جلب قائمة الشركات',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }

    _isLoadingCompanies.value = false;
  }

  // Set selected company
  void setSelectedCompany(CompanyModel? company) {
    _selectedCompany.value = company;
  }

  // Check if form has changes
  bool get hasChanges {
    return nameController.text.trim() != channel.name ||
        (notesController.text.trim().isEmpty
                ? null
                : notesController.text.trim()) !=
            channel.notes ||
        whatsappNumberController.text.trim() != channel.channelWhatsappNumber ||
        _selectedCompany.value?.id != channel.company.id;
  }

  // Validate form
  bool _validateForm() {
    if (!formKey.currentState!.validate()) {
      return false;
    }

    if (_selectedCompany.value == null) {
      Get.snackbar(
        'خطأ في التحقق',
        'يجب اختيار شركة',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    }

    if (!hasChanges) {
      Get.snackbar(
        'لا توجد تغييرات',
        'لم يتم إجراء أي تغييرات على البيانات',
        backgroundColor: Colors.orange,
        colorText: Colors.white,
      );
      return false;
    }

    return true;
  }

  // Update company channel
  Future<void> updateCompanyChannel() async {
    if (!canEditChannels) {
      Get.snackbar(
        'خطأ في الصلاحية',
        'ليس لديك صلاحية لتعديل قنوات الشركات',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    if (!_validateForm()) return;

    _isUpdating.value = true;

    // Format WhatsApp number
    final formattedNumber = _companyChannelService.formatWhatsAppNumber(
      whatsappNumberController.text.trim(),
    );

    final request = CompanyChannelEditRequest(
      companyId:
          _selectedCompany.value!.id != channel.company.id
              ? _selectedCompany.value!.id
              : null,
      name:
          nameController.text.trim() != channel.name
              ? nameController.text.trim()
              : null,
      notes:
          (notesController.text.trim().isEmpty
                      ? null
                      : notesController.text.trim()) !=
                  channel.notes
              ? (notesController.text.trim().isEmpty
                  ? null
                  : notesController.text.trim())
              : null,
      channelWhatsappNumber:
          formattedNumber != channel.channelWhatsappNumber
              ? formattedNumber
              : null,
    );

    final response = await _companyChannelService.updateCompanyChannel(
      channel.id!,
      request,
    );

    if (response.success) {
      Get.snackbar(
        'تم التحديث',
        'تم تحديث القناة بنجاح',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );

      // Navigate back to list
      Get.back(result: true);
    } else {
      Get.snackbar(
        'خطأ في التحديث',
        response.message ?? 'فشل في تحديث القناة',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }

    _isUpdating.value = false;
  }

  // Form validators
  String? validateName(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'اسم القناة مطلوب';
    }
    if (value.trim().length < 3) {
      return 'اسم القناة يجب أن يكون 3 أحرف على الأقل';
    }
    return null;
  }

  String? validateWhatsAppNumber(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'رقم الواتساب مطلوب';
    }

    final cleanNumber = value.replaceAll(RegExp(r'[^\d]'), '');
    if (cleanNumber.length < 10) {
      return 'رقم الواتساب يجب أن يكون 10 أرقام على الأقل';
    }

    if (!_companyChannelService.isValidWhatsAppNumber(value)) {
      return 'رقم الواتساب غير صحيح';
    }

    return null;
  }

  // Reset form to original values
  void resetForm() {
    _initializeForm();
    formKey.currentState?.reset();
  }
}
