import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:myrunway/core/models/company_channel_model.dart';
import 'package:myrunway/core/models/company_model.dart';
import 'package:myrunway/core/services/auth_service.dart';
import 'package:myrunway/core/services/company_channel_service.dart';
import 'package:myrunway/core/services/company_service.dart';
import 'package:myrunway/core/constants/user_roles.dart';

class CompanyChannelCreateController extends GetxController {
  final CompanyChannelService _companyChannelService =
      Get.find<CompanyChannelService>();
  final CompanyService _companyService = Get.find<CompanyService>();
  final AuthService _authService = Get.find<AuthService>();

  // Form controllers
  final nameController = TextEditingController();
  final notesController = TextEditingController();
  final whatsappNumberController = TextEditingController();

  // Form key
  final formKey = GlobalKey<FormState>();

  // Reactive variables
  final RxBool _isCreating = false.obs;
  final RxList<CompanyModel> _companies = <CompanyModel>[].obs;
  final RxBool _isLoadingCompanies = false.obs;
  final Rx<CompanyModel?> _selectedCompany = Rx<CompanyModel?>(null);
  final RxString _nameError = ''.obs;
  final RxString _whatsappError = ''.obs;
  final RxBool _isFormValid = false.obs;

  // Getters
  bool get isCreating => _isCreating.value;
  List<CompanyModel> get companies => _companies;
  bool get isLoadingCompanies => _isLoadingCompanies.value;
  CompanyModel? get selectedCompany => _selectedCompany.value;
  String get nameError => _nameError.value;
  String get whatsappError => _whatsappError.value;
  bool get isFormValid => _isFormValid.value;

  // Permission getters
  bool get canCreateChannels => _authService.hasPermission(UserRole.master);

  @override
  void onInit() {
    super.onInit();
    if (canCreateChannels) {
      loadCompanies();
    }

    // Add listeners for real-time validation
    nameController.addListener(_validateName);
    whatsappNumberController.addListener(_validateWhatsApp);
  }

  @override
  void onClose() {
    nameController.dispose();
    notesController.dispose();
    whatsappNumberController.dispose();
    super.onClose();
  }

  // Load companies for selection
  Future<void> loadCompanies() async {
    _isLoadingCompanies.value = true;

    final response = await _companyService.getCompanies();

    if (response.success && response.data != null) {
      _companies.value = response.data!;
    } else {
      Get.snackbar(
        'خطأ',
        response.message ?? 'فشل في جلب قائمة الشركات',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }

    _isLoadingCompanies.value = false;
  }

  // Set selected company
  void setSelectedCompany(CompanyModel? company) {
    _selectedCompany.value = company;
  }

  // Validate form
  bool _validateForm() {
    if (!formKey.currentState!.validate()) {
      return false;
    }

    if (_selectedCompany.value == null) {
      Get.snackbar(
        'خطأ في التحقق',
        'يجب اختيار شركة',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    }

    return true;
  }

  // Create company channel
  Future<void> createCompanyChannel() async {
    if (!canCreateChannels) {
      Get.snackbar(
        'خطأ في الصلاحية',
        'ليس لديك صلاحية لإنشاء قنوات الشركات',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    if (!_validateForm()) return;

    _isCreating.value = true;

    // Format WhatsApp number
    final formattedNumber = _companyChannelService.formatWhatsAppNumber(
      whatsappNumberController.text.trim(),
    );

    final request = CompanyChannelCreateRequest(
      companyId: _selectedCompany.value!.id!,
      name: nameController.text.trim(),
      notes:
          notesController.text.trim().isEmpty
              ? null
              : notesController.text.trim(),
      channelWhatsappNumber: formattedNumber,
    );

    final response = await _companyChannelService.createCompanyChannel(request);

    if (response.success) {
      Get.snackbar(
        'تم الإنشاء',
        'تم إنشاء القناة بنجاح',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );

      // Navigate back to list
      Get.back(result: true);
    } else {
      Get.snackbar(
        'خطأ في الإنشاء',
        response.message ?? 'فشل في إنشاء القناة',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }

    _isCreating.value = false;
  }

  // Form validators
  String? validateName(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'اسم القناة مطلوب';
    }
    if (value.trim().length < 3) {
      return 'اسم القناة يجب أن يكون 3 أحرف على الأقل';
    }
    return null;
  }

  String? validateWhatsAppNumber(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'رقم الواتساب مطلوب';
    }

    final cleanNumber = value.replaceAll(RegExp(r'[^\d]'), '');
    if (cleanNumber.length < 10) {
      return 'رقم الواتساب يجب أن يكون 10 أرقام على الأقل';
    }

    if (!_companyChannelService.isValidWhatsAppNumber(value)) {
      return 'رقم الواتساب غير صحيح';
    }

    return null;
  }

  // Clear form
  void clearForm() {
    nameController.clear();
    notesController.clear();
    whatsappNumberController.clear();
    _selectedCompany.value = null;
    _nameError.value = '';
    _whatsappError.value = '';
    _isFormValid.value = false;
    formKey.currentState?.reset();
  }

  // Real-time validation methods
  void _validateName() {
    final value = nameController.text.trim();
    if (value.isEmpty) {
      _nameError.value = 'اسم القناة مطلوب';
    } else if (value.length < 3) {
      _nameError.value = 'اسم القناة يجب أن يكون 3 أحرف على الأقل';
    } else {
      _nameError.value = '';
    }
    _updateFormValidity();
  }

  void _validateWhatsApp() {
    final value = whatsappNumberController.text.trim();
    if (value.isEmpty) {
      _whatsappError.value = 'رقم الواتساب مطلوب';
    } else {
      final cleanNumber = value.replaceAll(RegExp(r'[^\d]'), '');
      if (cleanNumber.length < 10) {
        _whatsappError.value = 'رقم الواتساب يجب أن يكون 10 أرقام على الأقل';
      } else if (!_companyChannelService.isValidWhatsAppNumber(value)) {
        _whatsappError.value = 'رقم الواتساب غير صحيح';
      } else {
        _whatsappError.value = '';
      }
    }
    _updateFormValidity();
  }

  void _updateFormValidity() {
    _isFormValid.value =
        _nameError.value.isEmpty &&
        _whatsappError.value.isEmpty &&
        nameController.text.trim().isNotEmpty &&
        whatsappNumberController.text.trim().isNotEmpty &&
        _selectedCompany.value != null;
  }

  // Validate company selection
  void validateCompanySelection() {
    _updateFormValidity();
  }
}
